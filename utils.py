#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫工具类
"""

import os
import json
import pandas as pd
from datetime import datetime
from urllib.parse import urljoin, urlparse
import re

class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def clean_text(text):
        """清理文本内容"""
        if not text:
            return ""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        return text
    
    @staticmethod
    def extract_telegram_info(url):
        """提取Telegram信息"""
        if 't.me' not in url:
            return None
            
        # 提取用户名或频道名
        match = re.search(r't\.me/([^/?]+)', url)
        if match:
            return {
                'username': match.group(1),
                'full_url': url,
                'type': 'bot' if 'bot' in url.lower() else 'channel'
            }
        return None
    
    @staticmethod
    def is_valid_url(url):
        """检查URL是否有效"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    @staticmethod
    def make_absolute_url(base_url, relative_url):
        """将相对URL转换为绝对URL"""
        return urljoin(base_url, relative_url)

class FileManager:
    """文件管理工具类"""
    
    @staticmethod
    def ensure_dir(directory):
        """确保目录存在"""
        os.makedirs(directory, exist_ok=True)
    
    @staticmethod
    def save_json(data, filepath):
        """保存JSON文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    @staticmethod
    def load_json(filepath):
        """加载JSON文件"""
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @staticmethod
    def save_excel(data_dict, filepath):
        """保存Excel文件"""
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            for sheet_name, data in data_dict.items():
                if isinstance(data, list):
                    df = pd.DataFrame(data)
                elif isinstance(data, dict):
                    df = pd.DataFrame([data])
                else:
                    continue
                df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    @staticmethod
    def generate_filename(prefix="data", extension="json"):
        """生成带时间戳的文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{prefix}_{timestamp}.{extension}"

class AnalysisTools:
    """数据分析工具类"""
    
    @staticmethod
    def analyze_links(links):
        """分析链接数据"""
        analysis = {
            'total_links': len(links),
            'telegram_links': 0,
            'external_links': 0,
            'internal_links': 0,
            'domains': set()
        }
        
        for link in links:
            url = link.get('url', '')
            
            if 't.me' in url:
                analysis['telegram_links'] += 1
            
            parsed = urlparse(url)
            if parsed.netloc:
                analysis['domains'].add(parsed.netloc)
                if 'app-core-x.online' in parsed.netloc:
                    analysis['internal_links'] += 1
                else:
                    analysis['external_links'] += 1
        
        analysis['unique_domains'] = len(analysis['domains'])
        analysis['domains'] = list(analysis['domains'])
        
        return analysis
    
    @staticmethod
    def analyze_content(text_content):
        """分析文本内容"""
        if not text_content:
            return {}

        words = text_content.split()

        # 关键词分析
        mining_keywords = ['mining', 'miner', 'mine', 'crypto', 'bitcoin', 'blockchain', 'hash', 'power', 'gpu', 'usdt']
        telegram_keywords = ['telegram', 'bot', 'channel', 'invite', 'friend', 'reward', 'referral']
        financial_keywords = ['withdraw', 'balance', 'exchange', 'rate', 'payment', 'transaction', 'unlock']

        keyword_counts = {}
        for category, keywords in [
            ('mining', mining_keywords),
            ('telegram', telegram_keywords),
            ('financial', financial_keywords)
        ]:
            category_counts = {}
            for keyword in keywords:
                count = len([w for w in words if keyword.lower() in w.lower()])
                if count > 0:
                    category_counts[keyword] = count

            if category_counts:
                keyword_counts[category] = category_counts

        # 提取数字和金额
        import re
        numbers = re.findall(r'\d+\.\d+|\d+', text_content)
        amounts = re.findall(r'\d+\.\d+\s*(?:USDT|GPU|hash|Hash)', text_content, re.IGNORECASE)

        return {
            'total_words': len(words),
            'total_chars': len(text_content),
            'keyword_counts': keyword_counts,
            'numbers_found': len(numbers),
            'amounts_found': amounts[:10] if amounts else []  # 只显示前10个
        }

class ReportGenerator:
    """报告生成器"""
    
    @staticmethod
    def generate_summary_report(data):
        """生成摘要报告"""
        report = {
            'crawl_time': datetime.now().isoformat(),
            'website_info': {
                'title': data.get('title', ''),
                'url': data.get('url', ''),
            },
            'statistics': {
                'total_links': len(data.get('links', [])),
                'telegram_links': len(data.get('telegram_links', [])),
                'images': len(data.get('images', [])),
                'mining_options': len(data.get('mining_options', []))
            }
        }
        
        # 链接分析
        if data.get('links'):
            link_analysis = AnalysisTools.analyze_links(data['links'])
            report['link_analysis'] = link_analysis
        
        # 内容分析
        if data.get('text_content'):
            content_analysis = AnalysisTools.analyze_content(data['text_content'])
            report['content_analysis'] = content_analysis
        
        return report
