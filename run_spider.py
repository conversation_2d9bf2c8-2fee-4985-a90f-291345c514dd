#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行CoreX爬虫的主脚本
"""

import sys
import argparse
from corex_spider import CoreXSpider
from utils import FileManager, ReportGenerator
from config import SAVE_CONFIG
from loguru import logger

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CoreX MINER 网站爬虫')
    parser.add_argument('--output', '-o', default='corex_data', 
                       help='输出文件名前缀 (默认: corex_data)')
    parser.add_argument('--format', '-f', choices=['json', 'excel', 'both'], 
                       default='both', help='输出格式 (默认: both)')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    try:
        # 创建必要的目录
        FileManager.ensure_dir(SAVE_CONFIG['data_dir'])
        FileManager.ensure_dir(SAVE_CONFIG['logs_dir'])
        
        # 初始化爬虫
        spider = CoreXSpider()
        
        # 运行爬虫
        logger.info("开始运行CoreX爬虫...")
        data = spider.run()
        
        # 生成报告
        report = ReportGenerator.generate_summary_report(data)
        
        # 保存数据
        if args.format in ['json', 'both']:
            json_file = f"{SAVE_CONFIG['data_dir']}/{args.output}.json"
            FileManager.save_json(data, json_file)
            logger.info(f"JSON数据已保存到: {json_file}")
        
        if args.format in ['excel', 'both']:
            excel_file = f"{SAVE_CONFIG['data_dir']}/{args.output}.xlsx"
            excel_data = {
                '基本信息': [report['website_info']],
                '统计信息': [report['statistics']],
                '链接': data.get('links', []),
                '图片': data.get('images', []),
                'Telegram链接': [{'url': url} for url in data.get('telegram_links', [])]
            }
            FileManager.save_excel(excel_data, excel_file)
            logger.info(f"Excel数据已保存到: {excel_file}")
        
        # 保存报告
        report_file = f"{SAVE_CONFIG['data_dir']}/{args.output}_report.json"
        FileManager.save_json(report, report_file)
        logger.info(f"分析报告已保存到: {report_file}")
        
        # 打印摘要
        print("\n" + "="*50)
        print("爬取完成！数据摘要:")
        print("="*50)
        print(f"网站标题: {data.get('title', 'N/A')}")
        print(f"挖矿选项: {', '.join(data.get('mining_options', []))}")
        print(f"总链接数: {len(data.get('links', []))}")
        print(f"Telegram链接: {len(data.get('telegram_links', []))}")
        print(f"图片数量: {len(data.get('images', []))}")
        
        if report.get('link_analysis'):
            la = report['link_analysis']
            print(f"外部链接: {la.get('external_links', 0)}")
            print(f"内部链接: {la.get('internal_links', 0)}")
            print(f"唯一域名: {la.get('unique_domains', 0)}")
        
        print("="*50)
        
        return True
        
    except Exception as e:
        logger.error(f"爬虫运行失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
