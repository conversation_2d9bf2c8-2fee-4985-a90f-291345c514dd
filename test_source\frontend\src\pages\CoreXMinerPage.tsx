import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import './CoreXMinerPage.scss';

const CoreXMinerPage: React.FC = () => {
  const updateButtonState = (swiper: any) => {
    const prevButton = document.querySelector('.custom-button-prev') as HTMLElement;
    const nextButton = document.querySelector('.custom-button-next') as HTMLElement;

    if (prevButton && nextButton) {
      // Hide "Prev" button on first slide
      if (swiper.isBeginning) {
        prevButton.style.display = 'none';
      } else {
        prevButton.style.display = 'block';
      }

      // Hide "Next" button on last slide
      if (swiper.isEnd) {
        nextButton.style.display = 'none';
      } else {
        nextButton.style.display = 'block';
      }
    }
  };

  return (
    <div className="corex-miner-page">
      <header className="header">
        <div className="container">
          <p className="header__text">CoreX MINER</p>
          <a href="https://t.me/Core_xbot" className="header__link">
            Cloud Mining in Telegram
          </a>
        </div>
      </header>
      
      <main>
        <section>
          <div className="container">
            <Swiper
              spaceBetween={30}
              slidesPerView={1}
              speed={500}
              modules={[Navigation]}
              navigation={{
                nextEl: '.custom-button-next',
                prevEl: '.custom-button-prev',
              }}
              onSlideChange={updateButtonState}
              onSwiper={updateButtonState}
              className="swiper swiper-custom"
            >
              <SwiperSlide>
                <h1 className="hero__title">CALCUMINER</h1>
                <video autoPlay muted loop playsInline className="hero__image">
                  <source src="/videos/one.mp4" type="video/mp4" />
                </video>
              </SwiperSlide>

              <SwiperSlide>
                <h1 className="hero__title">BLOCK BREACKER</h1>
                <video autoPlay muted loop playsInline className="hero__image">
                  <source src="/videos/two.MP4" type="video/mp4" />
                </video>
              </SwiperSlide>

              <SwiperSlide>
                <h1 className="hero__title">QUANTUM CORE</h1>
                <video autoPlay muted loop playsInline className="hero__image">
                  <source src="/videos/three.MP4" type="video/mp4" />
                </video>
              </SwiperSlide>

              <div className="custom-button-next" role="button" aria-label="下一张幻灯片" title="下一张幻灯片">
                <img className="hero__arrow-icon" src="/images/arrow-rigth.png" alt="下一张" />
              </div>
              <div className="custom-button-prev" role="button" aria-label="上一张幻灯片" title="上一张幻灯片">
                <img className="hero__arrow-icon" src="/images/arrow-left.png" alt="上一张" />
              </div>
            </Swiper>

            <a href="https://t.me/core_xbot" aria-label="开始挖矿 - 在Telegram中打开">
              <button className="btn" type="button">START MINING</button>
            </a>
            <p className="text">*Choose your graphic card and start mining in 1 click</p>
          </div>
        </section>
      </main>
    </div>
  );
};

export default CoreXMinerPage;
