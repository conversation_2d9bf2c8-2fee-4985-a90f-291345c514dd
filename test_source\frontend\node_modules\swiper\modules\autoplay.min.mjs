import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";function Autoplay(e){let a,t,{swiper:n,extendParams:i,on:r,emit:o,params:s}=e;n.autoplay={running:!1,paused:!1,timeLeft:0},i({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,p,u,d,y,m,c,g,v=s&&s.autoplay?s.autoplay.delay:3e3,T=s&&s.autoplay?s.autoplay.delay:3e3,f=(new Date).getTime();function w(e){n&&!n.destroyed&&n.wrapperEl&&e.target===n.wrapperEl&&(n.wrapperEl.removeEventListener("transitionend",w),g||e.detail&&e.detail.bySwiperTouchMove||S())}const b=()=>{if(n.destroyed||!n.autoplay.running)return;n.autoplay.paused?p=!0:p&&(T=l,p=!1);const e=n.autoplay.paused?l:f+T-(new Date).getTime();n.autoplay.timeLeft=e,o("autoplayTimeLeft",e,e/v),t=requestAnimationFrame((()=>{b()}))},E=e=>{if(n.destroyed||!n.autoplay.running)return;cancelAnimationFrame(t),b();let i=void 0===e?n.params.autoplay.delay:e;v=n.params.autoplay.delay,T=n.params.autoplay.delay;const r=(()=>{let e;if(e=n.virtual&&n.params.virtual.enabled?n.slides.find((e=>e.classList.contains("swiper-slide-active"))):n.slides[n.activeIndex],!e)return;return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(r)&&r>0&&void 0===e&&(i=r,v=r,T=r),l=i;const s=n.params.speed,p=()=>{n&&!n.destroyed&&(n.params.autoplay.reverseDirection?!n.isBeginning||n.params.loop||n.params.rewind?(n.slidePrev(s,!0,!0),o("autoplay")):n.params.autoplay.stopOnLastSlide||(n.slideTo(n.slides.length-1,s,!0,!0),o("autoplay")):!n.isEnd||n.params.loop||n.params.rewind?(n.slideNext(s,!0,!0),o("autoplay")):n.params.autoplay.stopOnLastSlide||(n.slideTo(0,s,!0,!0),o("autoplay")),n.params.cssMode&&(f=(new Date).getTime(),requestAnimationFrame((()=>{E()}))))};return i>0?(clearTimeout(a),a=setTimeout((()=>{p()}),i)):requestAnimationFrame((()=>{p()})),i},L=()=>{f=(new Date).getTime(),n.autoplay.running=!0,E(),o("autoplayStart")},D=()=>{n.autoplay.running=!1,clearTimeout(a),cancelAnimationFrame(t),o("autoplayStop")},O=(e,t)=>{if(n.destroyed||!n.autoplay.running)return;clearTimeout(a),e||(c=!0);const i=()=>{o("autoplayPause"),n.params.autoplay.waitForTransition?n.wrapperEl.addEventListener("transitionend",w):S()};if(n.autoplay.paused=!0,t)return m&&(l=n.params.autoplay.delay),m=!1,void i();const r=l||n.params.autoplay.delay;l=r-((new Date).getTime()-f),n.isEnd&&l<0&&!n.params.loop||(l<0&&(l=0),i())},S=()=>{n.isEnd&&l<0&&!n.params.loop||n.destroyed||!n.autoplay.running||(f=(new Date).getTime(),c?(c=!1,E(l)):E(),n.autoplay.paused=!1,o("autoplayResume"))},M=()=>{if(n.destroyed||!n.autoplay.running)return;const e=getDocument();"hidden"===e.visibilityState&&(c=!0,O(!0)),"visible"===e.visibilityState&&S()},h=e=>{"mouse"===e.pointerType&&(c=!0,g=!0,n.animating||n.autoplay.paused||O(!0))},A=e=>{"mouse"===e.pointerType&&(g=!1,n.autoplay.paused&&S())};r("init",(()=>{n.params.autoplay.enabled&&(n.params.autoplay.pauseOnMouseEnter&&(n.el.addEventListener("pointerenter",h),n.el.addEventListener("pointerleave",A)),getDocument().addEventListener("visibilitychange",M),L())})),r("destroy",(()=>{n.el&&"string"!=typeof n.el&&(n.el.removeEventListener("pointerenter",h),n.el.removeEventListener("pointerleave",A)),getDocument().removeEventListener("visibilitychange",M),n.autoplay.running&&D()})),r("_freeModeStaticRelease",(()=>{(d||c)&&S()})),r("_freeModeNoMomentumRelease",(()=>{n.params.autoplay.disableOnInteraction?D():O(!0,!0)})),r("beforeTransitionStart",((e,a,t)=>{!n.destroyed&&n.autoplay.running&&(t||!n.params.autoplay.disableOnInteraction?O(!0,!0):D())})),r("sliderFirstMove",(()=>{!n.destroyed&&n.autoplay.running&&(n.params.autoplay.disableOnInteraction?D():(u=!0,d=!1,c=!1,y=setTimeout((()=>{c=!0,d=!0,O(!0)}),200)))})),r("touchEnd",(()=>{if(!n.destroyed&&n.autoplay.running&&u){if(clearTimeout(y),clearTimeout(a),n.params.autoplay.disableOnInteraction)return d=!1,void(u=!1);d&&n.params.cssMode&&S(),d=!1,u=!1}})),r("slideChange",(()=>{!n.destroyed&&n.autoplay.running&&(m=!0)})),Object.assign(n.autoplay,{start:L,stop:D,pause:O,resume:S})}export{Autoplay as default};
//# sourceMappingURL=autoplay.min.mjs.map