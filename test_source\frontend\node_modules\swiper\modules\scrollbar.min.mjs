import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";import{m as makeElementsArray,j as classesToTokens,c as createElement,n as nextTick,d as elementOffset}from"../shared/utils.min.mjs";import{c as createElementIfNotDefined}from"../shared/create-element-if-not-defined.min.mjs";import{c as classesToSelector}from"../shared/classes-to-selector.min.mjs";function Scrollbar(s){let{swiper:l,extendParams:e,on:a,emit:r}=s;const t=getDocument();let o,n,i,c,p=!1,m=null,d=null;function b(){if(!l.params.scrollbar.el||!l.scrollbar.el)return;const{scrollbar:s,rtlTranslate:e}=l,{dragEl:a,el:r}=s,t=l.params.scrollbar,o=l.params.loop?l.progressLoop:l.progress;let c=n,p=(i-n)*o;e?(p=-p,p>0?(c=n-p,p=0):-p+n>i&&(c=i+p)):p<0?(c=n+p,p=0):p+n>i&&(c=i-p),l.isHorizontal()?(a.style.transform=`translate3d(${p}px, 0, 0)`,a.style.width=`${c}px`):(a.style.transform=`translate3d(0px, ${p}px, 0)`,a.style.height=`${c}px`),t.hide&&(clearTimeout(m),r.style.opacity=1,m=setTimeout((()=>{r.style.opacity=0,r.style.transitionDuration="400ms"}),1e3))}function u(){if(!l.params.scrollbar.el||!l.scrollbar.el)return;const{scrollbar:s}=l,{dragEl:e,el:a}=s;e.style.width="",e.style.height="",i=l.isHorizontal()?a.offsetWidth:a.offsetHeight,c=l.size/(l.virtualSize+l.params.slidesOffsetBefore-(l.params.centeredSlides?l.snapGrid[0]:0)),n="auto"===l.params.scrollbar.dragSize?i*c:parseInt(l.params.scrollbar.dragSize,10),l.isHorizontal()?e.style.width=`${n}px`:e.style.height=`${n}px`,a.style.display=c>=1?"none":"",l.params.scrollbar.hide&&(a.style.opacity=0),l.params.watchOverflow&&l.enabled&&s.el.classList[l.isLocked?"add":"remove"](l.params.scrollbar.lockClass)}function f(s){return l.isHorizontal()?s.clientX:s.clientY}function g(s){const{scrollbar:e,rtlTranslate:a}=l,{el:r}=e;let t;t=(f(s)-elementOffset(r)[l.isHorizontal()?"left":"top"]-(null!==o?o:n/2))/(i-n),t=Math.max(Math.min(t,1),0),a&&(t=1-t);const c=l.minTranslate()+(l.maxTranslate()-l.minTranslate())*t;l.updateProgress(c),l.setTranslate(c),l.updateActiveIndex(),l.updateSlidesClasses()}function y(s){const e=l.params.scrollbar,{scrollbar:a,wrapperEl:t}=l,{el:n,dragEl:i}=a;p=!0,o=s.target===i?f(s)-s.target.getBoundingClientRect()[l.isHorizontal()?"left":"top"]:null,s.preventDefault(),s.stopPropagation(),t.style.transitionDuration="100ms",i.style.transitionDuration="100ms",g(s),clearTimeout(d),n.style.transitionDuration="0ms",e.hide&&(n.style.opacity=1),l.params.cssMode&&(l.wrapperEl.style["scroll-snap-type"]="none"),r("scrollbarDragStart",s)}function h(s){const{scrollbar:e,wrapperEl:a}=l,{el:t,dragEl:o}=e;p&&(s.preventDefault&&s.cancelable?s.preventDefault():s.returnValue=!1,g(s),a.style.transitionDuration="0ms",t.style.transitionDuration="0ms",o.style.transitionDuration="0ms",r("scrollbarDragMove",s))}function T(s){const e=l.params.scrollbar,{scrollbar:a,wrapperEl:t}=l,{el:o}=a;p&&(p=!1,l.params.cssMode&&(l.wrapperEl.style["scroll-snap-type"]="",t.style.transitionDuration=""),e.hide&&(clearTimeout(d),d=nextTick((()=>{o.style.opacity=0,o.style.transitionDuration="400ms"}),1e3)),r("scrollbarDragEnd",s),e.snapOnRelease&&l.slideToClosest())}function v(s){const{scrollbar:e,params:a}=l,r=e.el;if(!r)return;const o=r,n=!!a.passiveListeners&&{passive:!1,capture:!1},i=!!a.passiveListeners&&{passive:!0,capture:!1};if(!o)return;const c="on"===s?"addEventListener":"removeEventListener";o[c]("pointerdown",y,n),t[c]("pointermove",h,n),t[c]("pointerup",T,i)}function D(){const{scrollbar:s,el:e}=l;l.params.scrollbar=createElementIfNotDefined(l,l.originalParams.scrollbar,l.params.scrollbar,{el:"swiper-scrollbar"});const a=l.params.scrollbar;if(!a.el)return;let r,o;if("string"==typeof a.el&&l.isElement&&(r=l.el.querySelector(a.el)),r||"string"!=typeof a.el)r||(r=a.el);else if(r=t.querySelectorAll(a.el),!r.length)return;l.params.uniqueNavElements&&"string"==typeof a.el&&r.length>1&&1===e.querySelectorAll(a.el).length&&(r=e.querySelector(a.el)),r.length>0&&(r=r[0]),r.classList.add(l.isHorizontal()?a.horizontalClass:a.verticalClass),r&&(o=r.querySelector(classesToSelector(l.params.scrollbar.dragClass)),o||(o=createElement("div",l.params.scrollbar.dragClass),r.append(o))),Object.assign(s,{el:r,dragEl:o}),a.draggable&&l.params.scrollbar.el&&l.scrollbar.el&&v("on"),r&&r.classList[l.enabled?"remove":"add"](...classesToTokens(l.params.scrollbar.lockClass))}function C(){const s=l.params.scrollbar,e=l.scrollbar.el;e&&e.classList.remove(...classesToTokens(l.isHorizontal()?s.horizontalClass:s.verticalClass)),l.params.scrollbar.el&&l.scrollbar.el&&v("off")}e({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),l.scrollbar={el:null,dragEl:null},a("changeDirection",(()=>{if(!l.scrollbar||!l.scrollbar.el)return;const s=l.params.scrollbar;let{el:e}=l.scrollbar;e=makeElementsArray(e),e.forEach((e=>{e.classList.remove(s.horizontalClass,s.verticalClass),e.classList.add(l.isHorizontal()?s.horizontalClass:s.verticalClass)}))})),a("init",(()=>{!1===l.params.scrollbar.enabled?E():(D(),u(),b())})),a("update resize observerUpdate lock unlock changeDirection",(()=>{u()})),a("setTranslate",(()=>{b()})),a("setTransition",((s,e)=>{!function(s){l.params.scrollbar.el&&l.scrollbar.el&&(l.scrollbar.dragEl.style.transitionDuration=`${s}ms`)}(e)})),a("enable disable",(()=>{const{el:s}=l.scrollbar;s&&s.classList[l.enabled?"remove":"add"](...classesToTokens(l.params.scrollbar.lockClass))})),a("destroy",(()=>{C()}));const E=()=>{l.el.classList.add(...classesToTokens(l.params.scrollbar.scrollbarDisabledClass)),l.scrollbar.el&&l.scrollbar.el.classList.add(...classesToTokens(l.params.scrollbar.scrollbarDisabledClass)),C()};Object.assign(l.scrollbar,{enable:()=>{l.el.classList.remove(...classesToTokens(l.params.scrollbar.scrollbarDisabledClass)),l.scrollbar.el&&l.scrollbar.el.classList.remove(...classesToTokens(l.params.scrollbar.scrollbarDisabledClass)),D(),u(),b()},disable:E,updateSize:u,setTranslate:b,init:D,destroy:C})}export{Scrollbar as default};
//# sourceMappingURL=scrollbar.min.mjs.map