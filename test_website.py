#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网站响应的简单脚本
"""

import requests
from fake_useragent import UserAgent
import json

def test_website():
    """测试网站响应"""
    url = "https://app-core-x.online//auth_m/1392053_ca4cea3e6d22bad"
    
    ua = UserAgent()
    headers = {
        'User-Agent': ua.random,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        print(f"正在测试: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {json.dumps(dict(response.headers), indent=2, ensure_ascii=False)}")
        print(f"内容长度: {len(response.content)} 字节")
        print(f"文本长度: {len(response.text)} 字符")
        print(f"编码: {response.encoding}")
        
        # 保存原始响应
        with open('raw_response.html', 'wb') as f:
            f.write(response.content)
        print("原始响应已保存到 raw_response.html")
        
        # 尝试不同的解码方式
        print("\n尝试不同的解码方式:")
        
        # UTF-8
        try:
            text_utf8 = response.content.decode('utf-8')
            print(f"UTF-8 解码成功，长度: {len(text_utf8)}")
            if len(text_utf8) < 1000:
                print(f"UTF-8 内容预览: {text_utf8[:500]}")
        except Exception as e:
            print(f"UTF-8 解码失败: {e}")
        
        # GBK
        try:
            text_gbk = response.content.decode('gbk')
            print(f"GBK 解码成功，长度: {len(text_gbk)}")
            if len(text_gbk) < 1000:
                print(f"GBK 内容预览: {text_gbk[:500]}")
        except Exception as e:
            print(f"GBK 解码失败: {e}")
        
        # ISO-8859-1
        try:
            text_iso = response.content.decode('iso-8859-1')
            print(f"ISO-8859-1 解码成功，长度: {len(text_iso)}")
            if len(text_iso) < 1000:
                print(f"ISO-8859-1 内容预览: {text_iso[:500]}")
        except Exception as e:
            print(f"ISO-8859-1 解码失败: {e}")
        
        # 检查是否是压缩内容
        if response.headers.get('content-encoding'):
            print(f"内容编码: {response.headers.get('content-encoding')}")
        
        # 检查内容类型
        content_type = response.headers.get('content-type', '')
        print(f"内容类型: {content_type}")
        
        # 尝试使用requests的自动解码
        print(f"\nRequests自动解码结果:")
        print(f"前500字符: {response.text[:500]}")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_website()
