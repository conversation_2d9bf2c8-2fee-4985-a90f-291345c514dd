# CoreX MINER 网站爬虫

这是一个用于爬取 CoreX MINER 网站数据的Python爬虫项目。

## 目标网站
- 主要目标: https://app-core-x.online//auth_m/1392053_ca4cea3e6d22bad
- 网站类型: 加密货币挖矿平台

## 功能特性

### 数据提取
- ✅ 网页标题和基本信息
- ✅ 所有链接（内部和外部）
- ✅ Telegram链接识别和提取
- ✅ 图片资源信息
- ✅ 挖矿相关选项
- ✅ 完整文本内容

### 技术特性
- 🔄 自动重试机制
- 🕐 随机延时防反爬虫
- 📝 详细日志记录
- 💾 多格式数据保存（JSON、Excel）
- 🔍 数据分析和报告生成
- 🛡️ 异常处理和错误恢复

## 项目结构

```
├── corex_spider.py     # 主爬虫类
├── run_spider.py       # 运行脚本
├── config.py          # 配置文件
├── utils.py           # 工具类
├── requirements.txt   # 依赖包
├── README.md         # 说明文档
├── data/             # 数据输出目录
└── logs/             # 日志文件目录
```

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行爬虫

#### 基本使用
```bash
python run_spider.py
```

#### 高级选项
```bash
# 指定输出文件名
python run_spider.py -o my_data

# 只保存JSON格式
python run_spider.py -f json

# 只保存Excel格式
python run_spider.py -f excel

# 详细输出模式
python run_spider.py -v

# 组合使用
python run_spider.py -o corex_mining_data -f both -v
```

### 3. 直接使用爬虫类
```python
from corex_spider import CoreXSpider

# 创建爬虫实例
spider = CoreXSpider()

# 运行爬虫
data = spider.run()

# 查看结果
print(data)
```

## 输出数据格式

### JSON格式
```json
{
  "title": "网站标题",
  "mining_options": ["CALCUMINER", "BLOCK BREACKER", "QUANTUM CORE"],
  "links": [
    {
      "url": "链接地址",
      "text": "链接文本",
      "is_telegram": true/false
    }
  ],
  "images": [
    {
      "src": "图片地址",
      "alt": "图片描述"
    }
  ],
  "telegram_links": ["t.me/链接1", "t.me/链接2"],
  "text_content": "完整文本内容"
}
```

### Excel格式
包含多个工作表：
- **基本信息**: 网站标题、挖矿选项等
- **统计信息**: 链接数量、图片数量等
- **链接**: 所有链接的详细信息
- **图片**: 图片资源信息
- **Telegram链接**: Telegram相关链接

## 配置说明

### 请求配置 (config.py)
- `timeout`: 请求超时时间
- `max_retries`: 最大重试次数
- `retry_delay`: 重试延时
- `random_delay_range`: 随机延时范围

### 爬取目标配置
- `extract_links`: 是否提取链接
- `extract_images`: 是否提取图片
- `extract_telegram_links`: 是否提取Telegram链接
- `extract_mining_info`: 是否提取挖矿信息

## 注意事项

### 合规使用
- 🚨 请遵守网站的robots.txt规则
- ⚖️ 遵守相关法律法规
- 🤝 尊重网站服务条款
- 📊 仅用于学习和研究目的

### 技术建议
- 💡 适当调整请求频率，避免对服务器造成压力
- 🔄 定期更新User-Agent和请求头
- 📝 监控日志文件，及时发现问题
- 💾 定期备份重要数据

## 常见问题

### Q: 爬虫运行失败怎么办？
A: 检查网络连接、查看日志文件、确认目标网站是否可访问

### Q: 如何修改爬取目标？
A: 修改 `config.py` 中的 `TARGET_URLS` 配置

### Q: 如何添加新的数据提取功能？
A: 在 `CoreXSpider` 类的 `parse_main_page` 方法中添加相应的解析逻辑

## 更新日志

- v1.0.0: 初始版本，支持基本的网页数据提取
- 支持多格式数据保存
- 集成数据分析和报告功能
