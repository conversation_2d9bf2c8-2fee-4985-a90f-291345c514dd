#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网站源码下载脚本
"""

import sys
import argparse
from source_downloader import SourceDownloader
from loguru import logger
import os

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CoreX MINER 网站源码下载器')
    
    # 基本参数
    parser.add_argument('--url', '-u', 
                       default='https://app-core-x.online',
                       help='网站基础URL')
    parser.add_argument('--output', '-o', 
                       default='corex_source',
                       help='输出目录名')
    
    # 页面URL列表
    parser.add_argument('--pages', '-p', nargs='+',
                       help='要下载的页面URL列表')
    parser.add_argument('--single-page', '-s',
                       help='下载单个页面及其资源')
    
    # 输出控制
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出模式')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='静默模式')
    
    # 安全选项
    parser.add_argument('--confirm', action='store_true',
                       help='确认开始下载（跳过警告）')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.quiet:
        logger.remove()
        logger.add(sys.stderr, level="ERROR")
    elif args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    else:
        logger.remove()
        logger.add(sys.stderr, level="INFO")
    
    # 安全警告
    if not args.confirm:
        print("⚠️  网站源码下载警告 ⚠️")
        print("="*50)
        print("你即将下载网站源码，这将：")
        print("• 下载HTML、CSS、JS、图片等所有资源")
        print("• 对目标服务器产生多个请求")
        print("• 在本地创建网站的完整副本")
        print("• 可能包含敏感信息")
        print()
        print("请确保：")
        print("• 你有权限下载该网站内容")
        print("• 遵守网站的服务条款")
        print("• 仅用于学习和研究目的")
        print("• 妥善保护下载的内容")
        print("="*50)
        
        confirm = input("确认继续？(y/N): ").lower().strip()
        if confirm != 'y':
            print("已取消下载")
            return False
    
    try:
        # 创建下载器实例
        downloader = SourceDownloader(
            base_url=args.url,
            output_dir=args.output
        )
        
        # 确定要下载的页面
        if args.single_page:
            # 下载单个页面
            pages = [args.single_page]
        elif args.pages:
            # 下载指定页面列表
            pages = args.pages
        else:
            # 默认页面列表
            pages = [
                f"{args.url}/auth_m/1392053_ca4cea3e6d22bad",  # 主页面
                f"{args.url}/account",                          # 账户页面
                f"{args.url}/account/tasks",                    # 任务页面
                f"{args.url}/account/payment",                  # 支付页面
                f"{args.url}/account/referals",                 # 推荐页面
                f"{args.url}/account/speed_up",                 # 加速页面
                f"{args.url}/account/faq",                      # FAQ页面
                f"{args.url}/account/support",                  # 支持页面
                f"{args.url}/account/history",                  # 历史页面
            ]
        
        # 显示下载参数
        logger.info("开始下载网站源码...")
        logger.info(f"目标网站: {args.url}")
        logger.info(f"输出目录: {args.output}")
        logger.info(f"页面数量: {len(pages)}")
        
        if args.verbose:
            logger.info("页面列表:")
            for i, page in enumerate(pages, 1):
                logger.info(f"  {i}. {page}")
        
        # 执行下载
        result = downloader.download_site(pages)
        
        # 显示结果摘要
        print("\n" + "="*60)
        print("🎉 网站源码下载完成！")
        print("="*60)
        print(f"📊 下载统计:")
        print(f"   • 成功页面: {result['success_count']}/{len(pages)}")
        print(f"   • 总文件数: {result['total_files']}")
        print(f"   • 总大小: {downloader.format_size(result['total_size'])}")
        print(f"   • 失败下载: {len(downloader.failed_downloads)}")
        
        print(f"\n📁 输出文件:")
        print(f"   • 源码目录: {args.output}/")
        print(f"   • 离线索引: {result['index_path']}")
        print(f"   • 下载报告: {result['report_path']}")
        
        print(f"\n📂 目录结构:")
        print(f"   {args.output}/")
        print(f"   ├── index.html          # 离线浏览索引")
        print(f"   ├── html/               # HTML页面")
        print(f"   ├── css/                # CSS样式文件")
        print(f"   ├── js/                 # JavaScript文件")
        print(f"   ├── images/             # 图片文件")
        print(f"   ├── fonts/              # 字体文件")
        print(f"   ├── assets/             # 其他资源")
        print(f"   └── download_report.json # 下载报告")
        
        if downloader.failed_downloads:
            print(f"\n⚠️  失败的下载:")
            for failed in downloader.failed_downloads[:5]:  # 只显示前5个
                print(f"   • {failed['url']} - {failed['error']}")
            if len(downloader.failed_downloads) > 5:
                print(f"   • ... 还有 {len(downloader.failed_downloads) - 5} 个失败")
        
        print(f"\n🌐 离线浏览:")
        print(f"   在浏览器中打开: {os.path.abspath(result['index_path'])}")
        
        print("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"下载失败: {str(e)}")
        import traceback
        if args.verbose:
            traceback.print_exc()
        return False

def show_examples():
    """显示使用示例"""
    examples = """
使用示例:

1. 下载默认页面（推荐）:
   py download_source.py --confirm

2. 下载单个页面及其资源:
   py download_source.py -s "https://app-core-x.online/account" --confirm

3. 下载指定页面列表:
   py download_source.py -p "https://app-core-x.online/account" "https://app-core-x.online/account/tasks" --confirm

4. 自定义输出目录:
   py download_source.py -o "my_website_backup" --confirm

5. 详细输出模式:
   py download_source.py -v --confirm

6. 静默模式:
   py download_source.py -q --confirm

输出说明:
• HTML文件保存在 html/ 目录
• CSS文件保存在 css/ 目录  
• JavaScript文件保存在 js/ 目录
• 图片文件保存在 images/ 目录
• 其他资源保存在 assets/ 目录
• index.html 提供离线浏览入口

注意事项:
• 首次运行需要 --confirm 参数确认
• 下载的文件可以离线浏览
• 请遵守网站服务条款和法律法规
• 妥善保护下载的敏感信息
"""
    print(examples)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("CoreX MINER 网站源码下载器")
        print("使用 --help 查看所有参数")
        print("使用 --examples 查看使用示例")
        sys.exit(0)
    
    if '--examples' in sys.argv:
        show_examples()
        sys.exit(0)
    
    success = main()
    sys.exit(0 if success else 1)
