<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Lekton:ital,wght@0,400;0,700;1,400&family=Rubik:ital,wght@0,300..900;1,300..900&family=Unbounded:wght@200..900&display=swap"
            rel="stylesheet"
        />
		
		<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
		
        <title>Account - Profile</title>
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.1/normalize.min.css"
            integrity="sha512-NhSC1YmyruXifcj/KFRWoC561YpHpc5Jtzgvbuzx5VozKpWvQ+4nXhPdFgmx8xqexRcpAglTj9sIBWINXa8x5w=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"
        />
        <link rel="stylesheet" href="/styles/styles.css?**********" />
    </head>
    <body>
        <div class="wrapper">
            <header class="header">
                <div class="container">
                    <nav class="header__nav">
                        <div class="menu__box">
                            <button class="header__menu-button">
                                <img
                                    class="header__menu-icon"
                                    src="/assets/burger-menu.png"
                                    alt="menu"
                                />
                            </button>
                            <!-- burger-menu -->

                            <ul class="burger__list">
                                <li><a href="/account/faq">FAQ</a></li>
                                <li><a href="/account/support">Support</a></li>
                                <li><a href="/account/referals">Referral program</a></li>
                                <li><a href="/account/history">Transaction history</a></li>
                            </ul>
                        </div>
						
						
						<div class="language__box">
	<div class="language__button">
		<img class="lang__img" src="/assets/lang/EN.png" alt="" />
	</div>
	
	<ul class="language__list">
		<li class="language__item" data-lang="EN">
			<img src="/assets/lang/EN.png" alt="" />
			<p>EN</p>
		</li>
		<li class="language__item" data-lang="RU">
			<img src="/assets/lang/RU.png" alt="" />
			<p>RU</p>
		</li>
		
		<!--
		<li class="language__item" data-lang="QQ">
			<img src="/assets/lang/QQ.png" alt="" />
			<p>TEST</p>
		</li>
		--> 
		
		<li class="language__item" data-lang="ES">
			<img src="/assets/lang/ES.png" alt="" />
			<p>ES</p>
		</li>
		
		<li class="language__item" data-lang="IT">
			<img src="/assets/lang/IT.png" alt="" />
			<p>IT</p>
		</li>
		
		<li class="language__item" data-lang="DE">
			<img src="/assets/lang/DE.png" alt="" />
			<p>DE</p>
		</li>
		
		<li class="language__item" data-lang="FR">
			<img src="/assets/lang/FR.png" alt="" />
			<p>FR</p>
		</li>
		
		<li class="language__item" data-lang="NL">
			<img src="/assets/lang/NL.png" alt="" />
			<p>NL</p>
		</li>
		
		<li class="language__item" data-lang="PL">
			<img src="/assets/lang/PL.png" alt="" />
			<p>PL</p>
		</li>
		
		<li class="language__item" data-lang="TR">
			<img src="/assets/lang/TR.png" alt="" />
			<p>TR</p>
		</li>
		
		<li class="language__item" data-lang="ID">
			<img src="/assets/lang/ID.png" alt="" />
			<p>ID</p>
		</li>
		
		
	</ul>
	
</div>
<script>
$(document).ready(function() {
    
	$(".language__item").click(function() {
		
        let selectedLang = $(this).data("lang"); // Получаем выбранный язык
        
		$.post("/AJAX/set_language.php", { lang: selectedLang })
		.done(function(response) {
			
			let data = JSON.parse(response);
			if (data.status === "success") {
			
				location.reload();
				
			} else {
				
				alert("Language ERROR: " + data.result);
				
			}
			
		}).fail(function() { 
			
			alert("Language ERROR #184");
			
		});

		
    });

});
</script>
						
						
                        <div class="header__balance">
                            <img src="/assets/header/money.png" alt="usdt" width="31" height="30" />
                            <p class="header__balance-amount" id="balance_top">0.000771</p>
                        </div>
                        <div class="header__user-plus">
                            <a href="/account/speed_up"><img src="/assets/header/plus.png" alt="plus" width="17" height="18" /></a>
                        </div>
                    </nav>
                </div>
            </header>
            <div class="scroll">
<script>
$(function() {

	$("#myDevice")[0].pause();

	balance = 34.************;
	
	speed = 0.*****************;
	speed = speed / 10;
	
	min_to_take = 1;;
	
	now_time = ********** ;
	mining_finish = ********** ;
	mining_now = false;
	old_mining_now = '';
	
	if (mining_finish > now_time) mining_now = true;
	
	statusMinerDev();
	
	first_checkMining = true;
	function checkMining() {
		// alert('old_mining_now: '+ old_mining_now + "  mining_now "+mining_now);
		if (old_mining_now !== mining_now) { // если статус изменился
			if (mining_now) {
				
				$("#statusText").text("Mining started").addClass("active");

				// Скрываем кнопку и показываем таймер
				$("#startButton").hide();
				$("#timer").show();

				$(".main").addClass("changed-bg");
				
				statusMinerDev();
				
			} else {
				
				statusMinerDev();
				
				$("#statusText").text("Mining started").removeClass("active");
				$("#timer").hide();
				$(".main").removeClass("changed-bg");
				$("#startButton").show();
				
				// уточним баланс на всякий случай
				if (!first_checkMining) {
					$.ajax({
						url: '/AJAX/mining_control.php',
						type: "GET",
						data: { 'action': 'getBalance'  },
						dataType: 'json',
						cache: false,
						success: function(res) {
							if (res['status'] =='OK'){
								$('#user_balance').text(res['coins']);
								$('#user_balance_nf').text(res['coins']);
							}
						}
					});
				}
			}
		}
		
		old_mining_now = mining_now;
		
		first_checkMining = false;
	}
	checkMining();
	setInterval(checkMining , 1000);
	
	
	function statusMinerDev(){
		
		const vDev = $("#myDevice")[0];
		
			if(mining_now){
			
				if(vDev.paused) vDev.play();
			
			}else{
				
				if(!vDev.paused) vDev.pause();
				
			}
		
	}
	

	
	
	function blinkTextColor() {
		const $element = $("#startButton");
		let direction = 1; // Направление изменения цвета (1 - к зеленому, -1 - к белому)
		let greenValue = 255; // Начальное значение зеленого компонента (255 - белый)

		setInterval(() => {
			// Изменяем значение красного и синего компонентов
			greenValue -= direction * 5; // Скорость изменения (5 единиц за итерацию)

			// Если достигнуты границы, меняем направление
			if (greenValue <= 0) {
				greenValue = 0;
				direction = -1; // Меняем направление на увеличение
			} else if (greenValue >= 255) {
				greenValue = 255;
				direction = 1; // Меняем направление на уменьшение
			}

			// Устанавливаем цвет текста через RGB (белый → зеленый и обратно)
			$element.css("color", `rgb(${greenValue}, 255, ${greenValue})`);
		}, 15); // Интервал обновления (30 мс)
	}

	
	
		
	function fixCoinBalance(balanceCoin){
		
		balanceCoin = parseFloat(balanceCoin);

		let f = 5;
		if(balanceCoin >= 100000) f = 4;
		if(balanceCoin >= 1000000) f = 3;
		if(balanceCoin >= 10000000) f = 2;
		if(balanceCoin >= 100000000) f = 1;
		if(balanceCoin >= 1000000000) f = 0;
		return balanceCoin.toFixed(f);
		
	}
	
	function mining_tick() {
		if (mining_now) {
			
			balance += speed;
			to_show = balance.toFixed(5);
			
			$('#user_balance').text( fixCoinBalance(to_show) );
			$('#user_balance_nf').text( fixCoinBalance(to_show) );
			//$('#exchangeCoinsMined').text( fixCoinBalance(balance.toFixed(5)) );
			
			
			$("#statusText").text("Mining started").addClass("active");
			
			// Скрываем кнопку и показываем таймер
			$("#startButton").hide();
			$("#timer").show();

			$(".main").addClass("changed-bg");
			
		}

		if (!mining_now) {
			$("#statusText").text("Mining stopped");
            $("#timer").hide();
            $(".main").removeClass("changed-bg");
			
		}		
		$('#exchangeCoinsMined').text( fixCoinBalance(balance.toFixed(5)) );
	};
	
	mining_tick();
	setInterval(mining_tick, 100);
	
	
	function tick() {
			now_time++;
				ttt = mining_finish - now_time;
				if (ttt <0) {
					ttt=0;
					mining_now = false;
				}
				tshour=Math.floor(ttt/3600); ttt -= tshour*3600; if(tshour<10)tshour='0'+tshour;
				tsec=ttt%60; ttt=Math.floor(ttt/60); if(tsec<10)tsec='0'+tsec;
				tmin=ttt%60; ttt=Math.floor(ttt/60); if(tmin<10)tmin='0'+tmin;
				//$('#timer_h').text(tshour);  $('#timer_m').text(tmin);  $('#timer_s').text(tsec);  
				
				$("#timer").text(
					`${String(tshour).padStart(2, "0")}:${String(tmin).padStart(2, "0")}:${String(tsec).padStart(2, "0")}`
				);
				
				
	}
	tick();
	setInterval(tick , 1000);
	
	
	function giftAndFirstStart(){
		
		if(speed > 0) return false;
		$("#fStatrCheck").html("<b>&#x2714;</b>");
		$("#firstTaskPressB").css("color", "#3F9C51");
		$("#fStatrCheck").css("color", "#3F9C51");
		$("#fStatrReward").css("color", "#FFC15A");
		
		setTimeout(function () {
			
			$("#firstTaskAnimQ").animate(
				{ 
					left: "-100%", // Сдвиг влево
					opacity: 0     // Исчезновение
				}, 
				1500, // Длительность анимации в миллисекундах
				function() {
					$("#firstTaskAnimQ").hide(); // Полностью скрыть элемент
				}
			);
			
		}, 1250);
		
		$("#mainMain").removeClass("main-blur");
		$("#amountGPU").html("1 000 GPU");
		
		speed = 0.000017806267806268;
		
		
		
		
		
	}
	
	
	$('#startMining').click(function() {
		
				
		$.ajax({
			url: '/AJAX/mining_control.php',
			type: "GET",
			data: { 'action': 'start_mining'  },
			dataType: 'json',
			cache: false,
			success: function(res) {
				if (res['error']=='need_auth') {
					window.location = "/";
					return false;
				}
				
				if (res['status'] =='OK') {
					mining_now = true;
					mining_finish = parseInt(res['mining_finish']);
					checkMining();
					balance = parseFloat(res['start_balance']);
					return false;
				};
				
			}, error: function(res) {
				//$('#message').html( 'Запрос не удался. Попробуйте еще раз.' );
			}
		});
		
		
		return false;
		
	});	
	

SHOW_HASH = true; // показываем в хэшах или рублях

$('.changeCurName').click(function() {
	SHOW_HASH = !SHOW_HASH;
	if (SHOW_HASH) {
		$('.hash_balanceName').text('Hashes');
		$('#hash_balance').text( ( parseFloat($('#hash_balance').text()) *100 ).toFixed(2) );
		
		balance *= 100;
		speed *= 100;
	} else {
		$('.hash_balanceName').text('FM Coin');
		
		balance /= 100;
		speed /= 100;
	}
});


//////

$('#exchangeCoins').click(function() {
	
	$.ajax({
		url: '/AJAX/mining_control.php',
		type: "GET",
		data: { 'action': 'takeBonus'  },
		dataType: 'json',
		cache: false,
		success: function(res) {
			
			
			if (res['error']=='need_auth') {
				window.location = "/";
				return;
			}
			
			
			if (res['error']=='min_sum') {
				
				$("#exchangeDiv").hide();
				$("#exchangeDivAnim").show();
				
				$('#exchangeAnimTitle').html('<font color="red">ERROR</font>');
				$('#exchangeAnimText').html("It is not possible to exchange less than 3 hashes");
				
				return;
			}
			
			
			if (res['status'] =='OK') {
				
				sum = parseFloat(res['sum']);
				
				$("#exchangeDiv").hide();
				$("#exchangeDivAnim").show();
				
				let new_balance = parseFloat(res['user_balance']);
				
				$('#balance_top').html(numberFormat(new_balance / 100, 6, ".", " ")); 
				$('#balance_index').html( numberFormat(new_balance / 100, 6, ".", " "));
				
				
				$('#exchangeAnimTitle').html('<font color="lime">SUCCESS</font>');
				$('#exchangeAnimText').html( numberFormat(sum / 100, 6, ".", " ") + ' USDT<BR /><BR />have been added to your balance');
				
				$('#user_balance_nf').html('0');
				$('#user_balance').html('0');
				balance = 0;
				

			};
			
		}, error: function(res) {
			alert( 'The request failed. Please try again.' );
		}
	});
	
	//$(".exchange-layout").removeClass("active");
    //$("body").removeClass("no-scroll");
	
});


$('#exchangeConfirm').click(function() {
	
	
	
	$(".exchange-layout").removeClass("active");
    $("body").removeClass("no-scroll");
	
	setTimeout(() => {
		
		$("#exchangeDivAnim").hide();
		$("#exchangeDiv").show();
		
	}, 500);
	
	
	
});




//////
	
});

function numberFormat(number, decimals = 0, decimalSeparator = ".", thousandSeparator = " ") {
    
	const fixedNumber = number.toFixed(decimals); 
    const parts = fixedNumber.split("."); 
    const integerPart = parts[0];
    const decimalPart = parts[1] || "";

    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);

    return decimalPart ? `${formattedInteger}${decimalSeparator}${decimalPart}` : formattedInteger;
}






</script>



<main class="main main-unlock " id="mainMain">
	<div class="swiper" >
		<div class="swiper-wrapper">
			<section class="hero swiper-slide">
				<div class="container hero__container">
					<div class="hero__image-wrapper" >
						<div class="hero__status no-scroll" id="startMining">
							<p class="hero__status-text" id="statusText">
								Mining stopped
							</p>
							<p class="hero__start-button" id="startButton">START</p>
							<p id="timer" class="hero__timer" style="display: none">
								22:52:12
							</p>
						</div>

						<div>
							<video id = "myDevice"
								autoplay
								muted
								loop
								playsinline
								class="hero__image"
							>
								<source
									src="/assets/hero/calcuminer.mp4"
									type="video/mp4"
								/>
							</video>
						</div>

					</div>
					
					<div class="hero__hashes" id="changeCurName">
						<p class="hero__hashes-count">
							<span id="user_balance">34.21048</span>
							<span class="hero__hashes-unit" id="hash_balanceName">Hashes</span>
						</p>
					</div>
					
				</div>
			</section>

						<section class="hero swiper-slide">
				<div class="container hero__container hero-unlock">
					<div class="hero__status--next">
						<p class="hero__status-text-next">Next mining machine</p>
						<button class="hero__start-button-next">
							Power to unlock: 248 750 GPU
						</button>

						<button class="hero__info-button">
							<img src="/assets/info.png" alt="info" />
						</button>
					</div>
					<div class="hero__image-wrapper hero__image-wrapper--next">
						<div class="hero__box">
							<video
								autoplay
								muted
								loop
								playsinline
								class="hero__image"
							>
								<source
									src="/assets/hero/basic-miner_locked.mp4"
									type="video/mp4"
								/>
							</video>
						</div>
					</div>
					<a href="/account/speed_up?unlock=24.93"><div class="hero__hashes unlock">UNLOCK</div></a>
				</div>
			</section>
						
						<section class="hero swiper-slide">
				<div class="container hero__container hero-unlock">
					<div class="hero__status--next">
						<p class="hero__status-text-next">Next mining machine</p>
						<button class="hero__start-button-next">
							Power to unlock: 998 750 GPU
						</button>

						<button class="hero__info-button">
							<img src="/assets/info.png" alt="info" />
						</button>
					</div>
					<div class="hero__image-wrapper hero__image-wrapper--next">
						<div class="hero__box">
							<video
								autoplay
								muted
								loop
								playsinline
								class="hero__image"
							>
								<source
									src="/assets/hero/IMG_1728_locked.MP4"
									type="video/mp4"
								/>
							</video>
						</div>
					</div>
					<a href="/account/speed_up?unlock=99.93"><div class="hero__hashes unlock">UNLOCK</div></a>
				</div>
			</section>
						
						<section class="hero swiper-slide">
				<div class="container hero__container hero-unlock">
					<div class="hero__status--next">
						<p class="hero__status-text-next">Next mining machine</p>
						<button class="hero__start-button-next">
							Power to unlock: 4 998 750 GPU
						</button>

						<button class="hero__info-button">
							<img src="/assets/info.png" alt="info" />
						</button>
					</div>
					<div class="hero__image-wrapper hero__image-wrapper--next">
						<div class="hero__box">
							<video
								autoplay
								muted
								loop
								playsinline
								class="hero__image"
							>
								<source
									src="/assets/hero/IMG_1729_locked.MP4"
									type="video/mp4"
								/>
							</video>
						</div>
					</div>
					<a href="/account/speed_up?unlock=499.93"><div class="hero__hashes unlock">UNLOCK</div></a>
				</div>
			</section>
						
						<section class="hero swiper-slide">
				<div class="container hero__container hero-unlock">
					<div class="hero__status--next">
						<p class="hero__status-text-next">Next mining machine</p>
						<button class="hero__start-button-next">
							Power to unlock: 9 998 750 GPU
						</button>

						<button class="hero__info-button">
							<img src="/assets/info.png" alt="info" />
						</button>
					</div>
					<div class="hero__image-wrapper hero__image-wrapper--next">
						<div class="hero__box">
							<video
								autoplay
								muted
								loop
								playsinline
								class="hero__image"
							>
								<source
									src="/assets/hero/IMG_1731_locked.MP4"
									type="video/mp4"
								/>
							</video>
						</div>
					</div>
					<a href="/account/speed_up?unlock=999.93"><div class="hero__hashes unlock">UNLOCK</div></a>
				</div>
			</section>
						
						<section class="hero swiper-slide">
				<div class="container hero__container hero-unlock">
					<div class="hero__status--next">
						<p class="hero__status-text-next">Next mining machine</p>
						<button class="hero__start-button-next">
							Power to unlock: 29 998 750 GPU
						</button>

						<button class="hero__info-button">
							<img src="/assets/info.png" alt="info" />
						</button>
					</div>
					<div class="hero__image-wrapper hero__image-wrapper--next">
						<div class="hero__box">
							<video
								autoplay
								muted
								loop
								playsinline
								class="hero__image"
							>
								<source
									src="/assets/hero/IMG_1737_locked.MP4"
									type="video/mp4"
								/>
							</video>
						</div>
					</div>
					<a href="/account/speed_up?unlock=2999.93"><div class="hero__hashes unlock">UNLOCK</div></a>
				</div>
			</section>
						
		</div>
		<div class="custom-button-next">
			<img
				class="hero__arrow-icon"
				src="/assets/arrow-rigth.png"
				alt="Next"
			/>
		</div>
		<div class="custom-button-prev">
			<img
				class="hero__arrow-icon"
				src="/assets/arrow-left.png"
				alt="Prev"
			/>
		</div>

		<div class="modal-backdrop">
			<div class="modal__info">
				<p class="modal__info--text">
					
	The 'Mining machine' is just a visual effect and doesn’t change
	the mining rate
	<br />
	<span>Mining speed depends on your POWER (GPU)</span>
				</p>
			</div>
		</div>
	</div>
		
		<script>
	// Акция STEP
	
		$(function() {
			var	time_last = 80284;
			function time() {
				time_last--;

				today = time_last;
				tsec=today%60; today=Math.floor(today/60); if(tsec<10)tsec='0'+tsec;
				tmin=today%60; today=Math.floor(today/60); if(tmin<10)tmin='0'+tmin;
				//thour=today%24; today=Math.floor(today/24);
				thour=today;
				if(thour<10) thour='0'+thour;
				if (thour==0) thour = '00';
				timestr= thour+" h "+tmin+" m "+tsec+" s";
				$('#actionCounter').each(function() {  $(this).html(timestr); } );
			}
			time() ;
			setInterval(time,1000);
		});
		
	</script>
	<div class="container">
		<aside class="sale">
			<a href="/account/speed_up" class="sale__link">
				<div class="sale__info">
					<p class="sale__title">Power sale</p>
					<p class="sale__offer">
						UP to 200% MORE POWER						<img class="sale__icon" src="/assets/gift.png" alt="" />
					</p>
				</div>
				<div class="sale__timer">
					<p class="sale__timer-title">Time left:</p>
					<p class="sale__time" id = 'actionCounter'>---</p>
				</div>
			</a>
		</aside>
	</div>
		
	
	
		
	
	<section class="power">
		<div class="container">
			<div class="power__content" style="border: 1px solid #343333">
				<p class="power__label">Current power</p>
				<p class="power__amount" id="amountGPU">1&nbsp;250 GPU</p>
				<a href="/account/speed_up" class="power__link">
					Buy power in the store				</a>
			</div>
		</div>
	</section>
	
	<section class="exchange">
		<div class="container">
			<p class="exchange__note">*Exchange Hashes to USDT to withdraw money</p>
			<div class="exchange__content">
				<div class="exchange__mined">
					<span class="exchange__label">Mined Hashes</span>
					<p class="exchange__amount" id="user_balance_nf" style="font-family: 'Lekton', monospace; font-weight: bold;">34.21048</p>
				</div>
				<button class="exchange__button">
					Exchange					<img
						class="exchange__icon"
						src="/assets/exchange-icon.png"
						alt="Exchange icon"
					/>
				</button>
			</div>
		</div>
		
		<div class="exchange-layout" id="exchangeDivlayout">
			<div class="exchange-modal">
			
				<div id="exchangeDiv">
				
					<p class="exchange-modal__title"  style="text-align: center;">Your result:<BR />
						<span id="exchangeCoinsMined">---</span> hashes
					</p>
					<p class="exchange-modal__text">
						Exchange rate: <BR />1 hash = 0.0001 USDT
					</p><BR />
					<p class="exchange-modal__text">Proceed with the exchange?</p>
					<div class="exchange-modal__actions">
						<button class="exchange-modal__button exchange-modal__button--confirm" style="width:130px;" id="exchangeCoins" >
							EXCHANGE						</button>
						<button class="exchange-modal__button exchange-modal__button--cancel" style="width:130px;">
							CANCEL						</button>
					</div>
				
				</div>
				
				<div id="exchangeDivAnim" style="display: none;">
				
					<p class="exchange-modal__title" id="exchangeAnimTitle" style="text-align: center;">SUCCESS</p>
					<p class="exchange-modal__text" id="exchangeAnimText">
						--- AIС added on your balance
					</p>
					<div class="exchange-modal__actions">
						<button class="exchange-modal__button" id="exchangeConfirm">
							OK						</button>
					</div>
				</div>
				
				
			</div>
		</div>
	</section>
	
		
	<section class="invite">
		<div class="container">
			<a href="https://t.me/share/url?url=https%3A%2F%2Ft.me%2Fcore_xbot%3Fstart%3D1392053&text=Cloud%20mining%20in%20just%201%20click%21%0AMine%20real%20USDT%20%E2%80%94%20withdraw%20easily%20anytime%21%0ADetails%20in%20the%20Bot%20%F0%9F%91%87%0A" class="invite__link">
				

				
				<div class="invite__container" style="border: 1px solid #343333">
					<div class="invite__image-wrapper invite__image-wrapper--plane">
						<img
							class="invite__image"
							src="/assets/plane.png"
							alt="Plane icon"
						/>
					</div>
					<div class="invite__details">
						<span class="invite__text">Invite 3 friends</span>
						<p class="invite__reward">Reward: 450 GPU</p>
					</div>
					<div class="invite__progress">1/3</div>
				</div>
				
				
			</a>
		</div>
	</section>
	</main>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />


<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script>
	
	
	const swiper = new Swiper(".swiper", {
		spaceBetween: 30,
		slidesPerView: 1,
		speed: 500,
		navigation: {
			nextEl: ".custom-button-next",
			prevEl: ".custom-button-prev",
		},
		on: {
			slideChange: updateButtonState,
		},
	});

	function updateButtonState() {
		const prevButton = document.querySelector(".custom-button-prev");
		const nextButton = document.querySelector(".custom-button-next");

		// Затемнить кнопку "Prev" на первом слайде
		if (swiper.isBeginning) {
			prevButton.style.display = "none";
		} else {
			prevButton.style.display = "block";
		}

		// Затемнить кнопку "Next" на последнем слайде
		if (swiper.isEnd) {
			nextButton.style.display = "none";
		} else {
			nextButton.style.display = "block";
		}
	}

	// Инициализация состояния кнопок при загрузке
	updateButtonState();
</script>
		
		
			
			
			
			
			


			</div>
            <footer class="footer">
                <div class="container">
                    <nav class="footer__nav">
                        <ul class="footer__list">
						
                            <li class="footer__item">
                                <a href="/account/speed_up" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/power.png"
                                        alt="Power"
                                    />
                                    <span class="footer__text">Power</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account/referals" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/earn.png"
                                        alt="Earn"
                                    />
                                    <span class="footer__text">Earn</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account" class="footer__link footer__link--active">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/miner.png"
                                        alt="Miner"
                                    />
                                    <span class="footer__text">Miner</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account/payment" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/withdraw.png"
                                        alt="Withdraw"
                                    />
                                    <span class="footer__text">Withdraw</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account/tasks" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/tasks.png"
                                        alt="Tasks"
                                    />
                                    <span class="footer__text">Tasks</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </footer>
        </div>

        <script type="module" src="/js/modal-info.js?7451055"></script>
    </body>
</html>
