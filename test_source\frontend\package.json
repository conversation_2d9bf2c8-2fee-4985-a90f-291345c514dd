{"name": "corex-miner-frontend", "version": "1.0.0", "description": "CoreX MINER Frontend Application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,scss}"}, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "antd-mobile": "^5.32.0", "axios": "^1.4.0", "dayjs": "^1.11.9", "i18next": "^23.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.2.0", "react-query": "^3.39.3", "react-redux": "^8.1.1", "react-router-dom": "^6.8.0", "socket.io-client": "^4.7.2", "swiper": "^11.2.10", "zustand": "^4.3.9"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^3.0.0", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.1"}, "engines": {"node": ">=18.0.0"}}