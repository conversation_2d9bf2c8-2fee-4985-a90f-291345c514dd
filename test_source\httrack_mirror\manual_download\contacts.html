<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Lekton:ital,wght@0,400;0,700;1,400&family=Rubik:ital,wght@0,300..900;1,300..900&family=Unbounded:wght@200..900&display=swap"
            rel="stylesheet"
        />
		
		<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
		
        <title>CoreX - Earn from cryptocurrency mining!</title>
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.1/normalize.min.css"
            integrity="sha512-NhSC1YmyruXifcj/KFRWoC561YpHpc5Jtzgvbuzx5VozKpWvQ+4nXhPdFgmx8xqexRcpAglTj9sIBWINXa8x5w=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"
        />
        <link rel="stylesheet" href="/styles/styles.css?**********" />
    </head>
    <body>
        <div class="wrapper">
            <header class="header">
                <div class="container">
                    <nav class="header__nav">
                        <div class="menu__box">
                            <button class="header__menu-button">
                                <img
                                    class="header__menu-icon"
                                    src="/assets/burger-menu.png"
                                    alt="menu"
                                />
                            </button>
                            <!-- burger-menu -->

                            <ul class="burger__list">
                                <li><a href="/account/faq">FAQ</a></li>
                                <li><a href="/account/support">Support</a></li>
                                <li><a href="/account/referals">Referral program</a></li>
                                <li><a href="/account/history">Transaction history</a></li>
                            </ul>
                        </div>
						
						
						<div class="language__box">
	<div class="language__button">
		<img class="lang__img" src="/assets/lang/EN.png" alt="" />
	</div>
	
	<ul class="language__list">
		<li class="language__item" data-lang="EN">
			<img src="/assets/lang/EN.png" alt="" />
			<p>EN</p>
		</li>
		<li class="language__item" data-lang="RU">
			<img src="/assets/lang/RU.png" alt="" />
			<p>RU</p>
		</li>
		
		<!--
		<li class="language__item" data-lang="QQ">
			<img src="/assets/lang/QQ.png" alt="" />
			<p>TEST</p>
		</li>
		--> 
		
		<li class="language__item" data-lang="ES">
			<img src="/assets/lang/ES.png" alt="" />
			<p>ES</p>
		</li>
		
		<li class="language__item" data-lang="IT">
			<img src="/assets/lang/IT.png" alt="" />
			<p>IT</p>
		</li>
		
		<li class="language__item" data-lang="DE">
			<img src="/assets/lang/DE.png" alt="" />
			<p>DE</p>
		</li>
		
		<li class="language__item" data-lang="FR">
			<img src="/assets/lang/FR.png" alt="" />
			<p>FR</p>
		</li>
		
		<li class="language__item" data-lang="NL">
			<img src="/assets/lang/NL.png" alt="" />
			<p>NL</p>
		</li>
		
		<li class="language__item" data-lang="PL">
			<img src="/assets/lang/PL.png" alt="" />
			<p>PL</p>
		</li>
		
		<li class="language__item" data-lang="TR">
			<img src="/assets/lang/TR.png" alt="" />
			<p>TR</p>
		</li>
		
		<li class="language__item" data-lang="ID">
			<img src="/assets/lang/ID.png" alt="" />
			<p>ID</p>
		</li>
		
		
	</ul>
	
</div>
<script>
$(document).ready(function() {
    
	$(".language__item").click(function() {
		
        let selectedLang = $(this).data("lang"); // Получаем выбранный язык
        
		$.post("/AJAX/set_language.php", { lang: selectedLang })
		.done(function(response) {
			
			let data = JSON.parse(response);
			if (data.status === "success") {
			
				location.reload();
				
			} else {
				
				alert("Language ERROR: " + data.result);
				
			}
			
		}).fail(function() { 
			
			alert("Language ERROR #184");
			
		});

		
    });

});
</script>
						
						
                        <div class="header__balance">
                            <img src="/assets/header/money.png" alt="usdt" width="31" height="30" />
                            <p class="header__balance-amount" id="balance_top">{!BALANCE!}</p>
                        </div>
                        <div class="header__user-plus">
                            <a href="/account/speed_up"><img src="/assets/header/plus.png" alt="plus" width="17" height="18" /></a>
                        </div>
                    </nav>
                </div>
            </header>
            <div class="scroll">			</div>
            <footer class="footer">
                <div class="container">
                    <nav class="footer__nav">
                        <ul class="footer__list">
						
                            <li class="footer__item">
                                <a href="/account/speed_up" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/power.png"
                                        alt="Power"
                                    />
                                    <span class="footer__text">Power</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account/referals" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/earn.png"
                                        alt="Earn"
                                    />
                                    <span class="footer__text">Earn</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/miner.png"
                                        alt="Miner"
                                    />
                                    <span class="footer__text">Miner</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account/payment" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/withdraw.png"
                                        alt="Withdraw"
                                    />
                                    <span class="footer__text">Withdraw</span>
                                </a>
                            </li>
                            <li class="footer__item">
                                <a href="/account/tasks" class="footer__link ">
                                    <img
                                        class="footer__icon"
                                        src="/assets/nav/tasks.png"
                                        alt="Tasks"
                                    />
                                    <span class="footer__text">Tasks</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </footer>
        </div>

        <script type="module" src="/js/modal-info.js?8836489"></script>
    </body>
</html>
