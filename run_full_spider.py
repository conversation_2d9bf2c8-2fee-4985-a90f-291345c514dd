#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行全站爬虫的脚本
"""

import sys
import argparse
from full_site_spider import FullSiteSpider
from loguru import logger
import os

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CoreX MINER 全站爬虫')
    
    # 基本参数
    parser.add_argument('--url', '-u', 
                       default='https://app-core-x.online/auth_m/1392053_ca4cea3e6d22bad',
                       help='起始URL')
    parser.add_argument('--base-url', '-b', 
                       default='https://app-core-x.online',
                       help='基础域名URL')
    
    # 爬取控制参数
    parser.add_argument('--max-depth', '-d', type=int, default=3,
                       help='最大爬取深度 (默认: 3)')
    parser.add_argument('--max-pages', '-p', type=int, default=100,
                       help='最大页面数量 (默认: 100)')
    
    # 输出控制
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出模式')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='静默模式')
    
    # 断点续传
    parser.add_argument('--checkpoint', '-c', 
                       help='从断点文件继续爬取')
    parser.add_argument('--save-checkpoint', action='store_true',
                       help='定期保存断点')
    
    # 安全选项
    parser.add_argument('--confirm', action='store_true',
                       help='确认开始全站爬取（跳过警告）')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.quiet:
        logger.remove()
        logger.add(sys.stderr, level="ERROR")
    elif args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    else:
        logger.remove()
        logger.add(sys.stderr, level="INFO")
    
    # 安全警告
    if not args.confirm:
        print("⚠️  全站爬取警告 ⚠️")
        print("="*50)
        print("你即将开始全站爬取，这将：")
        print("• 对目标服务器产生大量请求")
        print("• 可能需要较长时间完成")
        print("• 产生大量数据文件")
        print("• 可能触发反爬虫机制")
        print()
        print("请确保：")
        print("• 你有权限爬取该网站")
        print("• 遵守网站的robots.txt规则")
        print("• 不会对服务器造成过大压力")
        print("="*50)
        
        confirm = input("确认继续？(y/N): ").lower().strip()
        if confirm != 'y':
            print("已取消爬取")
            return False
    
    try:
        # 创建爬虫实例
        spider = FullSiteSpider(
            base_url=args.base_url,
            max_depth=args.max_depth,
            max_pages=args.max_pages
        )
        
        # 加载断点（如果指定）
        if args.checkpoint:
            if os.path.exists(args.checkpoint):
                spider.load_checkpoint(args.checkpoint)
                logger.info(f"从断点继续: {args.checkpoint}")
            else:
                logger.error(f"断点文件不存在: {args.checkpoint}")
                return False
        
        # 显示爬取参数
        logger.info("开始全站爬取...")
        logger.info(f"起始URL: {args.url}")
        logger.info(f"基础域名: {args.base_url}")
        logger.info(f"最大深度: {args.max_depth}")
        logger.info(f"最大页面数: {args.max_pages}")
        
        # 运行爬虫
        pages_data = spider.run(args.url)
        
        # 保存最终断点
        if args.save_checkpoint:
            checkpoint_file = spider.save_checkpoint()
            logger.info(f"最终断点已保存: {checkpoint_file}")
        
        # 显示结果摘要
        print("\n" + "="*60)
        print("🎉 全站爬取完成！")
        print("="*60)
        print(f"📊 爬取统计:")
        print(f"   • 成功页面: {len(spider.visited_urls)}")
        print(f"   • 失败页面: {len(spider.failed_urls)}")
        print(f"   • 发现链接: {spider.total_found}")
        print(f"   • 最大深度: {max(spider.site_structure.keys()) if spider.site_structure else 0}")
        
        print(f"\n📁 输出文件:")
        print(f"   • 页面数据: data/full_site/pages/")
        print(f"   • 汇总报告: data/full_site/site_summary_*.json")
        print(f"   • Excel报告: data/full_site/full_site_report_*.xlsx")
        
        if spider.failed_urls:
            print(f"\n⚠️  失败的URL:")
            for url in list(spider.failed_urls)[:5]:  # 只显示前5个
                print(f"   • {url}")
            if len(spider.failed_urls) > 5:
                print(f"   • ... 还有 {len(spider.failed_urls) - 5} 个")
        
        print("="*60)
        
        return True
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        
        # 保存中断时的断点
        if args.save_checkpoint:
            try:
                checkpoint_file = spider.save_checkpoint()
                logger.info(f"中断断点已保存: {checkpoint_file}")
                print(f"\n💾 断点已保存: {checkpoint_file}")
                print("可以使用 --checkpoint 参数继续爬取")
            except:
                pass
        
        return False
        
    except Exception as e:
        logger.error(f"爬取失败: {str(e)}")
        return False

def show_examples():
    """显示使用示例"""
    examples = """
使用示例:

1. 基本全站爬取（默认参数）:
   py run_full_spider.py --confirm

2. 限制深度和页面数:
   py run_full_spider.py -d 2 -p 50 --confirm

3. 详细输出模式:
   py run_full_spider.py -v --confirm

4. 从断点继续:
   py run_full_spider.py -c data/full_site/checkpoint_20250731_120000.pkl

5. 自定义起始URL:
   py run_full_spider.py -u "https://app-core-x.online/account" --confirm

6. 保存断点（用于大型爬取）:
   py run_full_spider.py -d 5 -p 500 --save-checkpoint --confirm

注意事项:
• 首次运行需要 --confirm 参数确认
• 建议先用小参数测试（如 -d 1 -p 10）
• 大型爬取建议使用 --save-checkpoint
• 遇到问题可以用 -v 查看详细日志
"""
    print(examples)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("CoreX MINER 全站爬虫")
        print("使用 --help 查看所有参数")
        print("使用 --examples 查看使用示例")
        sys.exit(0)
    
    if '--examples' in sys.argv:
        show_examples()
        sys.exit(0)
    
    success = main()
    sys.exit(0 if success else 1)
