// Power页面样式 - 完全按照原始网站设计风格
.power-page {
  min-height: 100vh;
  background-color: #000;
  color: #fff;
}

// 算力状态卡片
.power-status-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .power-status__header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    
    .power-icon {
      font-size: 32px;
      color: #ff9f00;
    }
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      margin: 0;
    }
  }
  
  .power-status__stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    
    .power-stat {
      text-align: center;
      padding: 15px;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 10px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .power-value {
        display: block;
        font-size: 28px;
        font-weight: bold;
        color: #ff9f00;
        margin-bottom: 5px;
        text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
      }
      
      .power-unit {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
      
      .hash-rate {
        display: block;
        font-size: 20px;
        font-weight: bold;
        color: #72b5e6;
        margin-bottom: 5px;
        text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
      }
      
      .hash-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

// 促销卡片
.promo-card {
  background: rgba(121, 222, 131, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(121, 222, 131, 0.3);
  
  .promo__content {
    display: flex;
    align-items: center;
    gap: 15px;
    
    .promo__icon {
      font-size: 32px;
      color: #79de83;
    }
    
    .promo__info {
      flex: 1;
      
      h4 {
        font-size: 20px;
        font-weight: 600;
        color: #fff;
        margin: 0 0 5px 0;
      }
      
      p {
        font-size: 16px;
        font-weight: 500;
        color: #79de83;
        margin: 0 0 5px 0;
      }
      
      .promo__timer {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

// 算力包区域
.packages-section {
  margin-bottom: 20px;
  
  .packages__header {
    text-align: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      margin: 0 0 10px 0;
    }
    
    p {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
    }
  }
  
  .packages__list {
    display: grid;
    gap: 15px;
    
    .package-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      padding: 20px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      }
      
      &.selected {
        border-color: #ff9f00;
        background: rgba(255, 159, 0, 0.1);
      }
      
      &.popular {
        border-color: #79de83;
        background: rgba(121, 222, 131, 0.1);
      }
      
      .popular-badge {
        position: absolute;
        top: -10px;
        right: 15px;
        background: #79de83;
        color: #000;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 5px;
      }
      
      .package__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        h4 {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin: 0;
        }
        
        .package__power {
          text-align: right;
          
          .power-amount {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: #ff9f00;
            text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
          }
          
          .power-unit {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
          }
        }
      }
      
      .package__details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .package__price {
          .price-amount {
            display: block;
            font-size: 20px;
            font-weight: bold;
            color: #72b5e6;
            text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
          }
          
          .price-currency {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
          }
        }
        
        .package__bonus {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 14px;
          color: #79de83;
          font-weight: 500;
        }
      }
    }
  }
}

// 购买按钮区域
.purchase-section {
  text-align: center;
  margin: 20px 0;
  
  .purchase-button {
    width: 255px;
    height: 56px;
    border: none;
    color: rgba(0, 0, 0, 0.9);
    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);
    font-size: 32px;
    font-style: normal;
    font-family: inherit;
    font-weight: 500;
    line-height: 17.551px;
    border-radius: 10px;
    background: #ff9f00;
    margin-bottom: 8px;
    margin-top: 7px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      background: #e68a00;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 159, 0, 0.3);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
  }
}

// 文本样式
.text {
  margin: auto;
  width: 270px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 17px;
  font-style: normal;
  font-weight: 400;
  line-height: 20.6px;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .power-status-card {
    padding: 15px;
    
    .power-status__stats {
      grid-template-columns: 1fr;
      gap: 15px;
      
      .power-stat {
        padding: 10px;
        
        .power-value {
          font-size: 24px;
        }
        
        .hash-rate {
          font-size: 18px;
        }
      }
    }
  }
  
  .packages__list .package-card {
    padding: 15px;
    
    .package__header {
      flex-direction: column;
      gap: 10px;
      text-align: center;
      
      .package__power {
        text-align: center;
      }
    }
    
    .package__details {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }
  }
  
  .purchase-button {
    width: 220px;
    font-size: 24px;
    height: 50px;
  }
  
  .text {
    width: 240px;
    font-size: 15px;
  }
} 