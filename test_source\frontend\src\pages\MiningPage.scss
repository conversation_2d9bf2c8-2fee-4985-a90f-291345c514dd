// 挖矿主页面样式
.mining-page {
  min-height: 100vh;
  background-color: #000;
  color: #fff;
}

// 挖矿状态卡片
.mining-status-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .mining-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      margin: 0;
    }
    
    .status-indicator {
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      
      &.active {
        background: #4CAF50;
        color: #fff;
      }
      
      &.inactive {
        background: #f44336;
        color: #fff;
      }
    }
  }
  
  .mining-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    
    .stat-item {
      text-align: center;
      padding: 15px;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 10px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #ff9f00;
        margin-bottom: 5px;
        text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
      }
      
      .stat-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

// Swiper 自定义样式
.swiper-custom {
  position: relative;
  max-width: 500px;
  margin: 0 auto 30px;
  
  .swiper {
    width: 100%;
    height: auto;
  }
  
  .swiper-slide {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

// 英雄标题样式
.hero__title {
  margin: auto;
  background-image: url('/images/block.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: cover;
  width: 300px;
  padding: 15px 0 17px;
  margin-bottom: 7px;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  font-size: 26px;
  font-style: normal;
  font-weight: 400;
  line-height: 20.6px;
  text-align: center;
}

// 英雄图片样式
.hero__image {
  width: 223px;
  height: auto;
}

@media screen and (min-width: 768px) {
  .hero__image {
    width: 277px;
  }
}

// 导航按钮样式
.custom-button-prev,
.custom-button-next {
  position: absolute;
  top: 60%;
  transform: translateY(-50%);
  z-index: 10;
  color: #fff;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }
}

.custom-button-prev {
  left: 10px;
}

.custom-button-next {
  right: 10px;
}

// 箭头图标样式
.hero__arrow-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

// 控制按钮区域
.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
  align-items: center;
  
  .btn {
    width: 255px;
    height: 56px;
    border: none;
    color: rgba(0, 0, 0, 0.9);
    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);
    font-size: 32px;
    font-style: normal;
    font-family: inherit;
    font-weight: 500;
    line-height: 17.551px;
    border-radius: 10px;
    background: #ff9f00;
    margin-bottom: 8px;
    margin-top: 7px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #e68a00;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 159, 0, 0.3);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    
    &.start-btn {
      background: #4CAF50;
      
      &:hover {
        background: #45a049;
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
      }
    }
    
    &.stop-btn {
      background: #f44336;
      
      &:hover {
        background: #da190b;
        box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
      }
    }
    
    &.exchange-btn {
      background: #2196F3;
      
      &:hover {
        background: #1976D2;
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
      }
    }
  }
}

// 快速操作区域
.quick-actions {
  margin-bottom: 30px;
  
  .action-link {
    text-decoration: none;
    display: block;
    margin-bottom: 20px;
  }
  
  .action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
    
    .action-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      padding: 20px;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      }
      
      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 10px;
      }
      
      p {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 15px;
        line-height: 1.4;
      }
      
      .btn-small {
        width: 100%;
        height: 40px;
        border: none;
        color: #fff;
        font-size: 16px;
        font-weight: 500;
        border-radius: 8px;
        background: #ff9f00;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: #e68a00;
          transform: translateY(-2px);
        }
      }
    }
  }
}

// 文本样式
.text {
  margin: auto;
  width: 270px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 17px;
  font-style: normal;
  font-weight: 400;
  line-height: 20.6px;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .mining-status-card {
    padding: 15px;
    
    .mining-status-header {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }
    
    .mining-stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      
      .stat-item {
        padding: 10px;
        
        .stat-value {
          font-size: 20px;
        }
        
        .stat-label {
          font-size: 12px;
        }
      }
    }
  }
  
  .control-buttons .btn {
    width: 220px;
    font-size: 24px;
    height: 50px;
  }
  
  .quick-actions .action-cards {
    grid-template-columns: 1fr;
  }
  
  .hero__title {
    width: 250px;
    font-size: 22px;
  }
  
  .text {
    width: 240px;
    font-size: 15px;
  }
  
  .hero__image {
    width: 200px;
  }
} 