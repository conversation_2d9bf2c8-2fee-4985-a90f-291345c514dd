#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全站数据分析工具
"""

import json
import os
import pandas as pd
from collections import defaultdict, Counter
import re
from urllib.parse import urlparse
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

class FullSiteAnalyzer:
    def __init__(self, data_dir="data/full_site"):
        self.data_dir = data_dir
        self.pages_data = {}
        self.summary_data = {}
        
    def load_data(self):
        """加载所有爬取的数据"""
        pages_dir = os.path.join(self.data_dir, "pages")
        
        if not os.path.exists(pages_dir):
            print(f"数据目录不存在: {pages_dir}")
            return False
            
        # 加载所有页面数据
        page_files = [f for f in os.listdir(pages_dir) if f.endswith('.json')]
        
        for file in page_files:
            file_path = os.path.join(pages_dir, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    page_data = json.load(f)
                    url = page_data.get('url')
                    if url:
                        self.pages_data[url] = page_data
            except Exception as e:
                print(f"加载页面数据失败: {file}, 错误: {e}")
                
        # 加载汇总数据
        summary_files = [f for f in os.listdir(self.data_dir) 
                        if f.startswith('site_summary_') and f.endswith('.json')]
        
        if summary_files:
            latest_summary = sorted(summary_files)[-1]
            summary_path = os.path.join(self.data_dir, latest_summary)
            try:
                with open(summary_path, 'r', encoding='utf-8') as f:
                    self.summary_data = json.load(f)
            except Exception as e:
                print(f"加载汇总数据失败: {e}")
                
        print(f"已加载 {len(self.pages_data)} 个页面的数据")
        return True
        
    def analyze_site_structure(self):
        """分析网站结构"""
        print("\n" + "="*60)
        print("🏗️  网站结构分析")
        print("="*60)
        
        # URL层级分析
        url_levels = defaultdict(list)
        url_patterns = defaultdict(int)
        
        for url in self.pages_data.keys():
            parsed = urlparse(url)
            path_parts = [part for part in parsed.path.split('/') if part]
            level = len(path_parts)
            url_levels[level].append(url)
            
            # 分析URL模式
            if path_parts:
                pattern = '/' + '/'.join(['*' if re.match(r'\d+', part) else part for part in path_parts])
                url_patterns[pattern] += 1
                
        print(f"📊 URL层级分布:")
        for level in sorted(url_levels.keys()):
            print(f"   层级 {level}: {len(url_levels[level])} 个页面")
            
        print(f"\n🔗 常见URL模式:")
        for pattern, count in sorted(url_patterns.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   {pattern}: {count} 次")
            
        return url_levels, url_patterns
        
    def analyze_content_types(self):
        """分析内容类型"""
        print("\n" + "="*60)
        print("📄 内容类型分析")
        print("="*60)
        
        page_types = defaultdict(int)
        title_keywords = Counter()
        
        for url, page_data in self.pages_data.items():
            title = page_data.get('title', '').lower()
            
            # 分类页面类型
            if any(keyword in title for keyword in ['login', 'auth', 'sign']):
                page_types['认证页面'] += 1
            elif any(keyword in title for keyword in ['account', 'profile', 'dashboard']):
                page_types['账户页面'] += 1
            elif any(keyword in title for keyword in ['payment', 'withdraw', 'deposit']):
                page_types['支付页面'] += 1
            elif any(keyword in title for keyword in ['faq', 'help', 'support']):
                page_types['帮助页面'] += 1
            elif any(keyword in title for keyword in ['mining', 'miner', 'mine']):
                page_types['挖矿页面'] += 1
            elif any(keyword in title for keyword in ['referral', 'invite', 'earn']):
                page_types['推荐页面'] += 1
            else:
                page_types['其他页面'] += 1
                
            # 统计标题关键词
            words = re.findall(r'\b\w+\b', title)
            for word in words:
                if len(word) > 2:  # 忽略太短的词
                    title_keywords[word] += 1
                    
        print(f"📊 页面类型分布:")
        for page_type, count in sorted(page_types.items(), key=lambda x: x[1], reverse=True):
            print(f"   {page_type}: {count} 个")
            
        print(f"\n🔤 常见标题关键词:")
        for keyword, count in title_keywords.most_common(15):
            print(f"   {keyword}: {count} 次")
            
        return page_types, title_keywords
        
    def analyze_links_and_navigation(self):
        """分析链接和导航结构"""
        print("\n" + "="*60)
        print("🔗 链接和导航分析")
        print("="*60)
        
        all_links = []
        internal_links = []
        external_links = []
        telegram_links = []
        
        link_targets = Counter()
        
        for url, page_data in self.pages_data.items():
            page_links = page_data.get('links', [])
            
            for link in page_links:
                link_url = link.get('url', '')
                link_text = link.get('text', '').strip()
                
                all_links.append(link_url)
                
                if link.get('is_telegram'):
                    telegram_links.append(link_url)
                elif link_url.startswith('/') or 'app-core-x.online' in link_url:
                    internal_links.append(link_url)
                    # 统计内部链接目标
                    if link_text:
                        link_targets[link_text] += 1
                else:
                    external_links.append(link_url)
                    
        print(f"📊 链接统计:")
        print(f"   总链接数: {len(all_links)}")
        print(f"   内部链接: {len(internal_links)}")
        print(f"   外部链接: {len(external_links)}")
        print(f"   Telegram链接: {len(telegram_links)}")
        
        print(f"\n🎯 常见导航目标:")
        for target, count in link_targets.most_common(10):
            print(f"   {target}: {count} 次")
            
        # 分析Telegram链接
        if telegram_links:
            print(f"\n📱 Telegram链接分析:")
            unique_tg_links = set(telegram_links)
            for tg_link in unique_tg_links:
                print(f"   {tg_link}")
                
        return {
            'total_links': len(all_links),
            'internal_links': len(internal_links),
            'external_links': len(external_links),
            'telegram_links': len(telegram_links),
            'link_targets': dict(link_targets)
        }
        
    def analyze_forms_and_interactions(self):
        """分析表单和交互元素"""
        print("\n" + "="*60)
        print("📝 表单和交互分析")
        print("="*60)
        
        total_forms = 0
        form_methods = Counter()
        form_actions = Counter()
        pages_with_forms = 0
        
        for url, page_data in self.pages_data.items():
            forms = page_data.get('forms', [])
            
            if forms:
                pages_with_forms += 1
                
            for form in forms:
                total_forms += 1
                method = form.get('method', 'get').upper()
                action = form.get('action', '')
                
                form_methods[method] += 1
                
                if action:
                    # 简化action路径
                    if action.startswith('/'):
                        form_actions[action] += 1
                    else:
                        form_actions['外部表单'] += 1
                        
        print(f"📊 表单统计:")
        print(f"   总表单数: {total_forms}")
        print(f"   有表单的页面: {pages_with_forms}")
        
        if form_methods:
            print(f"\n📤 表单方法分布:")
            for method, count in form_methods.items():
                print(f"   {method}: {count} 个")
                
        if form_actions:
            print(f"\n🎯 表单目标:")
            for action, count in sorted(form_actions.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {action}: {count} 个")
                
        return {
            'total_forms': total_forms,
            'pages_with_forms': pages_with_forms,
            'form_methods': dict(form_methods),
            'form_actions': dict(form_actions)
        }
        
    def analyze_images_and_assets(self):
        """分析图片和资源"""
        print("\n" + "="*60)
        print("🖼️  图片和资源分析")
        print("="*60)
        
        total_images = 0
        image_types = Counter()
        image_dirs = Counter()
        
        for url, page_data in self.pages_data.items():
            images = page_data.get('images', [])
            total_images += len(images)
            
            for img in images:
                src = img.get('src', '')
                
                if src:
                    # 分析图片类型
                    if '.' in src:
                        ext = src.split('.')[-1].lower()
                        image_types[ext] += 1
                        
                    # 分析图片目录
                    if '/assets/' in src:
                        parts = src.split('/assets/')[-1].split('/')
                        if len(parts) > 1:
                            directory = parts[0]
                            image_dirs[directory] += 1
                        else:
                            image_dirs['root'] += 1
                            
        print(f"📊 图片统计:")
        print(f"   总图片数: {total_images}")
        
        if image_types:
            print(f"\n🎨 图片类型分布:")
            for img_type, count in image_types.most_common():
                print(f"   .{img_type}: {count} 个")
                
        if image_dirs:
            print(f"\n📁 图片目录分布:")
            for directory, count in image_dirs.most_common():
                print(f"   {directory}: {count} 个")
                
        return {
            'total_images': total_images,
            'image_types': dict(image_types),
            'image_directories': dict(image_dirs)
        }
        
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        if not self.load_data():
            return
            
        print("🔍 开始全站数据分析...")
        
        # 执行各项分析
        structure_data = self.analyze_site_structure()
        content_data = self.analyze_content_types()
        links_data = self.analyze_links_and_navigation()
        forms_data = self.analyze_forms_and_interactions()
        images_data = self.analyze_images_and_assets()
        
        # 生成总结
        print("\n" + "="*60)
        print("📋 分析总结")
        print("="*60)
        print(f"🌐 网站规模: {len(self.pages_data)} 个页面")
        print(f"🔗 链接总数: {links_data['total_links']}")
        print(f"🖼️  图片总数: {images_data['total_images']}")
        print(f"📝 表单总数: {forms_data['total_forms']}")
        print(f"📱 Telegram链接: {links_data['telegram_links']}")
        
        # 保存详细报告
        report_data = {
            'analysis_time': datetime.now().isoformat(),
            'site_overview': {
                'total_pages': len(self.pages_data),
                'total_links': links_data['total_links'],
                'total_images': images_data['total_images'],
                'total_forms': forms_data['total_forms']
            },
            'structure_analysis': structure_data,
            'content_analysis': content_data,
            'links_analysis': links_data,
            'forms_analysis': forms_data,
            'images_analysis': images_data
        }
        
        report_file = f"{self.data_dir}/comprehensive_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
            
        print(f"\n💾 详细报告已保存: {report_file}")
        print("="*60)

def main():
    analyzer = FullSiteAnalyzer()
    analyzer.generate_comprehensive_report()

if __name__ == "__main__":
    main()
