function effectInit(e){const{effect:s,swiper:a,on:t,setTranslate:r,setTransition:i,overwriteParams:n,perspective:o,recreateShadows:f,getEffectParams:l}=e;let c;t("beforeInit",(()=>{if(a.params.effect!==s)return;a.classNames.push(`${a.params.containerModifierClass}${s}`),o&&o()&&a.classNames.push(`${a.params.containerModifierClass}3d`);const e=n?n():{};Object.assign(a.params,e),Object.assign(a.originalParams,e)})),t("setTranslate _virtualUpdated",(()=>{a.params.effect===s&&r()})),t("setTransition",((e,t)=>{a.params.effect===s&&i(t)})),t("transitionEnd",(()=>{if(a.params.effect===s&&f){if(!l||!l().slideShadows)return;a.slides.forEach((e=>{e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((e=>e.remove()))})),f()}})),t("virtualUpdate",(()=>{a.params.effect===s&&(a.slides.length||(c=!0),requestAnimationFrame((()=>{c&&a.slides&&a.slides.length&&(r(),c=!1)})))}))}export{effectInit as e};
//# sourceMappingURL=effect-init.min.mjs.map