#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析爬虫结果的脚本
"""

import json
import pandas as pd
from urllib.parse import urlparse, unquote
import re

def analyze_corex_data():
    """分析CoreX爬虫数据"""
    
    # 读取数据
    try:
        with open('data/corex_data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("未找到数据文件，请先运行爬虫")
        return
    
    print("="*60)
    print("CoreX MINER 网站爬虫分析报告")
    print("="*60)
    
    # 基本信息
    print(f"\n📊 基本信息:")
    print(f"   网站标题: {data.get('title', 'N/A')}")
    print(f"   总链接数: {len(data.get('links', []))}")
    print(f"   图片数量: {len(data.get('images', []))}")
    print(f"   Telegram链接: {len(data.get('telegram_links', []))}")
    
    # 链接分析
    print(f"\n🔗 链接分析:")
    links = data.get('links', [])
    
    # 按类型分类链接
    internal_links = [link for link in links if link['url'].startswith('/')]
    external_links = [link for link in links if not link['url'].startswith('/')]
    telegram_links = [link for link in links if link['is_telegram']]
    
    print(f"   内部链接: {len(internal_links)}")
    print(f"   外部链接: {len(external_links)}")
    print(f"   Telegram链接: {len(telegram_links)}")
    
    # 显示重要链接
    print(f"\n📱 重要功能链接:")
    important_paths = ['/account/payment', '/account/referals', '/account/tasks', '/account/speed_up']
    for link in links:
        if any(path in link['url'] for path in important_paths):
            print(f"   - {link['text']}: {link['url']}")
    
    # Telegram链接详细分析
    print(f"\n📞 Telegram链接详情:")
    for tg_link in data.get('telegram_links', []):
        decoded_url = unquote(tg_link)
        print(f"   - {decoded_url}")
        
        # 提取bot信息
        if 'core_xbot' in decoded_url:
            print(f"     Bot名称: core_xbot")
            if 'start=' in decoded_url:
                start_param = re.search(r'start=(\d+)', decoded_url)
                if start_param:
                    print(f"     推荐ID: {start_param.group(1)}")
    
    # 挖矿相关信息分析
    print(f"\n⛏️ 挖矿相关信息:")
    text_content = data.get('text_content', '')
    
    # 提取数值信息
    gpu_values = re.findall(r'(\d+(?:,\d+)*)\s*GPU', text_content)
    hash_values = re.findall(r'(\d+\.\d+)\s*[Hh]ash', text_content)
    usdt_values = re.findall(r'(\d+\.\d+)\s*USDT', text_content)
    
    if gpu_values:
        print(f"   GPU功率值: {', '.join(gpu_values)}")
    if hash_values:
        print(f"   Hash值: {', '.join(hash_values)}")
    if usdt_values:
        print(f"   USDT值: {', '.join(usdt_values)}")
    
    # 解锁价格分析
    unlock_links = [link for link in links if 'unlock=' in link['url']]
    if unlock_links:
        print(f"\n💰 解锁价格:")
        prices = []
        for link in unlock_links:
            price_match = re.search(r'unlock=(\d+\.\d+)', link['url'])
            if price_match:
                prices.append(float(price_match.group(1)))
        
        if prices:
            prices.sort()
            print(f"   价格范围: ${min(prices)} - ${max(prices)}")
            print(f"   所有价格: {', '.join([f'${p}' for p in prices])}")
    
    # 功能模块分析
    print(f"\n🎯 功能模块:")
    modules = {
        'FAQ': '/account/faq',
        '客服支持': '/account/support', 
        '推荐计划': '/account/referals',
        '交易历史': '/account/history',
        '提现': '/account/payment',
        '任务': '/account/tasks',
        '加速/购买': '/account/speed_up'
    }
    
    for module_name, path in modules.items():
        found = any(path in link['url'] for link in links)
        status = "✅" if found else "❌"
        print(f"   {status} {module_name}")
    
    # 图片资源分析
    print(f"\n🖼️ 图片资源:")
    images = data.get('images', [])
    image_types = {}
    for img in images:
        src = img.get('src', '')
        if '/assets/' in src:
            folder = src.split('/assets/')[-1].split('/')[0] if '/' in src.split('/assets/')[-1] else 'root'
            image_types[folder] = image_types.get(folder, 0) + 1
    
    for folder, count in image_types.items():
        print(f"   {folder}: {count} 张图片")
    
    # 安全和合规性分析
    print(f"\n🔒 安全性观察:")
    
    # 检查是否有可疑的高收益承诺
    suspicious_keywords = ['guarantee', '保证', 'risk-free', '无风险', 'easy money', '轻松赚钱']
    found_suspicious = []
    for keyword in suspicious_keywords:
        if keyword.lower() in text_content.lower():
            found_suspicious.append(keyword)
    
    if found_suspicious:
        print(f"   ⚠️ 发现可疑营销词汇: {', '.join(found_suspicious)}")
    else:
        print(f"   ✅ 未发现明显的可疑营销词汇")
    
    # 检查是否有明确的风险提示
    risk_keywords = ['risk', '风险', 'loss', '损失', 'investment', '投资']
    found_risk_warnings = []
    for keyword in risk_keywords:
        if keyword.lower() in text_content.lower():
            found_risk_warnings.append(keyword)
    
    if found_risk_warnings:
        print(f"   ✅ 发现风险相关词汇: {', '.join(found_risk_warnings)}")
    else:
        print(f"   ⚠️ 未发现明确的风险提示")
    
    print(f"\n" + "="*60)
    print("分析完成！")
    print("="*60)
    
    # 生成简要总结
    print(f"\n📋 总结:")
    print(f"这是一个加密货币云挖矿平台，主要特点：")
    print(f"• 通过Telegram Bot进行推广和用户获取")
    print(f"• 提供多级解锁机制（价格从${min(prices) if 'prices' in locals() and prices else 'N/A'}到${max(prices) if 'prices' in locals() and prices else 'N/A'}）")
    print(f"• 支持推荐计划和任务系统")
    print(f"• 提供Hash到USDT的兑换功能")
    print(f"• 具备完整的账户管理功能（FAQ、客服、提现等）")

if __name__ == "__main__":
    analyze_corex_data()
