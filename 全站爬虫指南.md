# CoreX MINER 全站爬虫使用指南

## 🎯 功能概述

全站爬虫可以递归爬取整个网站的所有页面，提供比单页面爬虫更全面的数据收集和分析能力。

### ✨ 主要特性

- 🕷️ **递归爬取**: 自动发现并爬取网站内所有链接
- 🎛️ **深度控制**: 可设置最大爬取深度和页面数量
- 💾 **断点续传**: 支持中断后继续爬取
- 📊 **全面分析**: 提供网站结构、内容、链接等多维度分析
- 🛡️ **安全机制**: 内置反爬虫对策和请求频率控制
- 📁 **多格式输出**: JSON、Excel、分析报告等多种格式

## 🚀 快速开始

### 1. 基本使用

```bash
# 确认开始全站爬取（首次使用必须加 --confirm）
py run_full_spider.py --confirm

# 这将使用默认参数：
# - 最大深度: 3层
# - 最大页面: 100个
# - 起始URL: https://app-core-x.online/auth_m/1392053_ca4cea3e6d22bad
```

### 2. 自定义参数

```bash
# 限制爬取范围（推荐测试用）
py run_full_spider.py -d 2 -p 20 --confirm

# 详细输出模式
py run_full_spider.py -v --confirm

# 大规模爬取（建议保存断点）
py run_full_spider.py -d 5 -p 500 --save-checkpoint --confirm
```

### 3. 断点续传

```bash
# 保存断点
py run_full_spider.py --save-checkpoint --confirm

# 从断点继续
py run_full_spider.py -c data/full_site/checkpoint_20250731_120000.pkl
```

## 📊 输出文件结构

```
data/full_site/
├── pages/                          # 单个页面数据
│   ├── abc123.json                 # 页面1数据
│   ├── def456.json                 # 页面2数据
│   └── ...
├── site_summary_20250731_120000.json    # 爬取汇总
├── full_site_report_20250731_120000.xlsx # Excel报告
└── checkpoint_20250731_120000.pkl       # 断点文件
```

### 📄 数据格式说明

#### 单页面数据 (pages/*.json)
```json
{
  "url": "页面URL",
  "title": "页面标题",
  "meta_description": "页面描述",
  "links": [
    {
      "url": "链接地址",
      "text": "链接文本",
      "is_telegram": true/false
    }
  ],
  "images": [...],
  "forms": [...],
  "text_content": "完整文本内容",
  "crawl_time": "爬取时间"
}
```

#### 汇总报告 (site_summary_*.json)
```json
{
  "crawl_info": {
    "total_crawled": 85,
    "total_failed": 5,
    "max_depth": 3
  },
  "site_structure": {
    "0": ["首页URL"],
    "1": ["一级页面URLs"],
    "2": ["二级页面URLs"]
  },
  "url_analysis": {...},
  "content_analysis": {...}
}
```

## 🔧 参数详解

### 基本参数
- `-u, --url`: 起始URL（默认：登录页面）
- `-b, --base-url`: 基础域名（默认：https://app-core-x.online）

### 爬取控制
- `-d, --max-depth`: 最大深度（默认：3）
- `-p, --max-pages`: 最大页面数（默认：100）

### 输出控制
- `-v, --verbose`: 详细输出
- `-q, --quiet`: 静默模式

### 断点功能
- `-c, --checkpoint`: 从断点文件继续
- `--save-checkpoint`: 保存断点

### 安全选项
- `--confirm`: 确认开始（跳过警告）

## 📈 数据分析

### 运行分析工具

```bash
# 分析全站数据
py analyze_full_site.py
```

### 分析内容包括

1. **🏗️ 网站结构分析**
   - URL层级分布
   - 常见URL模式
   - 页面深度统计

2. **📄 内容类型分析**
   - 页面类型分类
   - 标题关键词统计
   - 内容主题分析

3. **🔗 链接和导航分析**
   - 内部/外部链接统计
   - 导航目标分析
   - Telegram链接提取

4. **📝 表单和交互分析**
   - 表单数量和类型
   - 表单方法分布
   - 交互元素统计

5. **🖼️ 图片和资源分析**
   - 图片类型分布
   - 资源目录结构
   - 静态资源统计

## ⚠️ 重要注意事项

### 法律和道德考虑
- ✅ **确保合规**: 检查网站robots.txt和服务条款
- ✅ **尊重服务器**: 控制请求频率，避免过大压力
- ✅ **数据保护**: 妥善处理爬取的敏感信息
- ✅ **用途限制**: 仅用于学习、研究或授权用途

### 技术建议
- 🔄 **先小后大**: 建议先用小参数测试（如 -d 1 -p 10）
- 💾 **保存断点**: 大规模爬取务必使用 --save-checkpoint
- 📝 **监控日志**: 使用 -v 参数查看详细进度
- ⏰ **合理延时**: 内置2-5秒随机延时，请勿修改

### 故障排除
- **爬取中断**: 使用断点文件继续
- **权限错误**: 检查目标URL是否需要登录
- **网络超时**: 检查网络连接和目标网站状态
- **内存不足**: 减少max-pages参数

## 📋 使用示例

### 示例1: 快速测试
```bash
# 爬取2层深度，最多20个页面
py run_full_spider.py -d 2 -p 20 -v --confirm
```

### 示例2: 完整爬取
```bash
# 爬取完整网站，保存断点
py run_full_spider.py -d 4 -p 200 --save-checkpoint --confirm
```

### 示例3: 断点续传
```bash
# 从断点继续爬取
py run_full_spider.py -c data/full_site/checkpoint_20250731_120000.pkl -v
```

### 示例4: 自定义起始点
```bash
# 从账户页面开始爬取
py run_full_spider.py -u "https://app-core-x.online/account" -d 3 --confirm
```

## 🎯 预期结果

根据CoreX MINER网站的特点，全站爬取预期发现：

### 主要功能模块
- 👤 **用户认证**: 登录、注册页面
- 💰 **账户管理**: 个人资料、余额、历史
- ⛏️ **挖矿功能**: 挖矿控制、算力管理
- 💸 **支付系统**: 充值、提现、交易
- 🎯 **任务系统**: 日常任务、奖励机制
- 👥 **推荐系统**: 邀请链接、奖励分配
- ❓ **帮助支持**: FAQ、客服、文档

### 技术发现
- 📱 **Telegram集成**: Bot链接、分享机制
- 💎 **加密货币**: USDT、Hash等数字资产
- 🔐 **安全机制**: 认证、授权、加密
- 📊 **数据接口**: API端点、数据交换

## 🔄 后续分析

爬取完成后，可以进行：

1. **商业模式分析**: 了解平台盈利机制
2. **用户流程分析**: 梳理用户操作路径
3. **技术架构分析**: 研究系统设计模式
4. **安全评估**: 识别潜在安全问题
5. **竞品对比**: 与其他平台功能对比

---

**⚠️ 免责声明**: 本工具仅供学习和研究使用，使用者需确保遵守相关法律法规和网站服务条款。
