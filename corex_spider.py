#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoreX MINER 网站爬虫
目标网站: https://app-core-x.online//auth_m/1392053_ca4cea3e6d22bad
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
from fake_useragent import UserAgent
from loguru import logger
from retrying import retry
import pandas as pd
from urllib.parse import urljoin, urlparse
import os
from datetime import datetime
import brotli
import gzip

class CoreXSpider:
    def __init__(self):
        """初始化爬虫"""
        self.base_url = "https://app-core-x.online"
        self.target_url = "https://app-core-x.online//auth_m/1392053_ca4cea3e6d22bad"
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        self.setup_logger()
        
    def setup_logger(self):
        """设置日志"""
        logger.add("logs/corex_spider_{time}.log", 
                  rotation="1 day", 
                  retention="7 days",
                  level="INFO")
        
    def setup_session(self):
        """设置请求会话"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
        }
        self.session.headers.update(headers)
        
    def decompress_content(self, response):
        """解压缩响应内容"""
        content_encoding = response.headers.get('content-encoding', '').lower()
        content = response.content

        try:
            if content_encoding == 'br':
                logger.info("检测到Brotli压缩，尝试解压缩...")
                # 尝试使用requests的自动解压缩
                if hasattr(response, 'raw') and hasattr(response.raw, 'read'):
                    return response.text
                else:
                    # 手动解压缩
                    decompressed = brotli.decompress(content)
                    return decompressed.decode('utf-8')
            elif content_encoding == 'gzip':
                logger.info("检测到Gzip压缩，尝试解压缩...")
                decompressed = gzip.decompress(content)
                return decompressed.decode('utf-8')
            elif content_encoding == 'deflate':
                logger.info("检测到Deflate压缩，尝试解压缩...")
                import zlib
                decompressed = zlib.decompress(content)
                return decompressed.decode('utf-8')
            else:
                logger.info("未检测到压缩，使用原始内容")
                return response.text
        except Exception as e:
            logger.warning(f"解压缩失败: {e}，尝试使用原始内容")
            return response.text

    @retry(stop_max_attempt_number=3, wait_fixed=2000)
    def fetch_page(self, url):
        """获取网页内容"""
        try:
            logger.info(f"正在访问: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '').lower()
            content_encoding = response.headers.get('content-encoding', '')
            logger.info(f"响应内容类型: {content_type}")
            logger.info(f"内容编码: {content_encoding}")
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"原始内容长度: {len(response.content)} 字节")

            # 解压缩内容
            html_content = self.decompress_content(response)
            logger.info(f"解压后内容长度: {len(html_content)} 字符")

            # 创建一个新的响应对象来返回解压后的内容
            class DecompressedResponse:
                def __init__(self, original_response, decompressed_text):
                    self.original = original_response
                    self.status_code = original_response.status_code
                    self.headers = original_response.headers
                    self.text = decompressed_text
                    self.content = decompressed_text.encode('utf-8')
                    self.url = original_response.url

                def raise_for_status(self):
                    return self.original.raise_for_status()

            decompressed_response = DecompressedResponse(response, html_content)

            # 随机延时，避免被反爬虫
            time.sleep(random.uniform(1, 3))

            return decompressed_response
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {str(e)}")
            raise
            
    def parse_main_page(self, html_content):
        """解析主页面内容"""
        soup = BeautifulSoup(html_content, 'lxml')
        
        data = {
            'title': '',
            'mining_options': [],
            'links': [],
            'images': [],
            'text_content': '',
            'telegram_links': []
        }
        
        # 提取标题
        title = soup.find('title')
        if title:
            data['title'] = title.get_text().strip()
            
        # 提取所有链接
        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href']
            text = link.get_text().strip()
            data['links'].append({
                'url': href,
                'text': text,
                'is_telegram': 't.me' in href
            })
            
            # 特别收集Telegram链接
            if 't.me' in href:
                data['telegram_links'].append(href)
                
        # 提取图片
        images = soup.find_all('img')
        for img in images:
            src = img.get('src', '')
            alt = img.get('alt', '')
            data['images'].append({
                'src': src,
                'alt': alt
            })
            
        # 提取挖矿选项（根据网页内容）
        mining_sections = soup.find_all(text=True)
        mining_keywords = ['CALCUMINER', 'BLOCK BREACKER', 'QUANTUM CORE', 'START MINING']
        
        for keyword in mining_keywords:
            if any(keyword in text for text in mining_sections):
                data['mining_options'].append(keyword)
                
        # 提取所有文本内容
        data['text_content'] = soup.get_text().strip()
        
        return data
        
    def save_data(self, data, filename=None):
        """保存数据到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"corex_data_{timestamp}"
            
        # 创建数据目录
        os.makedirs('data', exist_ok=True)
        
        # 保存为JSON
        json_file = f"data/{filename}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"数据已保存到: {json_file}")
        
        # 保存为Excel（如果有结构化数据）
        if data.get('links'):
            excel_file = f"data/{filename}.xlsx"
            
            # 链接数据
            links_df = pd.DataFrame(data['links'])
            
            # 图片数据
            images_df = pd.DataFrame(data['images']) if data['images'] else pd.DataFrame()
            
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                links_df.to_excel(writer, sheet_name='链接', index=False)
                if not images_df.empty:
                    images_df.to_excel(writer, sheet_name='图片', index=False)
                    
                # 基本信息
                basic_info = pd.DataFrame([{
                    '标题': data['title'],
                    '挖矿选项': ', '.join(data['mining_options']),
                    'Telegram链接数量': len(data['telegram_links']),
                    '总链接数量': len(data['links']),
                    '图片数量': len(data['images'])
                }])
                basic_info.to_excel(writer, sheet_name='基本信息', index=False)
                
            logger.info(f"Excel文件已保存到: {excel_file}")
            
    def run(self):
        """运行爬虫"""
        try:
            logger.info("开始爬取CoreX MINER网站...")
            
            # 获取主页面
            response = self.fetch_page(self.target_url)
            
            # 解析数据
            data = self.parse_main_page(response.text)
            
            # 保存数据
            self.save_data(data)
            
            # 打印摘要
            logger.info("爬取完成！数据摘要:")
            logger.info(f"- 网站标题: {data['title']}")
            logger.info(f"- 挖矿选项: {', '.join(data['mining_options'])}")
            logger.info(f"- 找到链接: {len(data['links'])} 个")
            logger.info(f"- Telegram链接: {len(data['telegram_links'])} 个")
            logger.info(f"- 图片: {len(data['images'])} 个")
            
            return data
            
        except Exception as e:
            logger.error(f"爬虫运行失败: {str(e)}")
            raise

if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    
    # 运行爬虫
    spider = CoreXSpider()
    result = spider.run()
