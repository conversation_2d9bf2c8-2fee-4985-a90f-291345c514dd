const modalBackdrop = document.querySelector(".modal-backdrop");
const infoButton = document.querySelectorAll(".hero__info-button");
const menuButton = document.querySelector(".header__menu-button");
const burgerLayout = document.querySelector(".burger__list");
const exchangeIcon = document.querySelector(".exchange__icon");
const exchangeButton = document.querySelector(".exchange__button");
const layout = document.querySelector(".exchange-layout");
const cancelButton = document.querySelector(".exchange-modal__button--cancel");

const languageButton = document.querySelector(".language__button");
const languageLayout = document.querySelector(".language__list");


const openModalButton = document.getElementById("open-modal");
const modalOverlay = document.getElementById("modal-overlay");
// Показываем модальное окно
if (infoButton) {
    infoButton.forEach((element) => {
        element.addEventListener("click", () => {
            modalBackdrop.classList.add("show");
        });
    });
}

// Закрываем модальное окно при нажатии вне его
if (modalBackdrop) {
    modalBackdrop.addEventListener("click", (event) => {
        if (event.target === modalBackdrop) {
            modalBackdrop.classList.remove("show");
        }
    });
}

// menu

menuButton.addEventListener("click", () => {
    if (languageLayout.classList.contains("active")) {
        languageLayout.classList.remove("active");
    }

    burgerLayout.classList.toggle("active");
});

languageButton.addEventListener("click", () => {
    if (burgerLayout.classList.contains("active")) {
        burgerLayout.classList.remove("active");
    }

    languageLayout.classList.toggle("active");
});


// exchange

// Обработка нажатия на кнопку обмена
if (exchangeButton) {
    exchangeButton.addEventListener("click", () => {
        // Запуск анимации вращения
        exchangeIcon.classList.add("spin");

        // Удаление анимации после завершения
        exchangeIcon.addEventListener("animationend", () => {
            exchangeIcon.classList.remove("spin");
        }, { once: true });

        // Показ всплывающего окна
        layout.classList.add("active");
        document.body.classList.add("no-scroll");
    });
}

// Обработка нажатия на кнопку "CANCEL"
if (cancelButton) {
    cancelButton.addEventListener("click", () => {
        // Скрытие всплывающего окна
        layout.classList.remove("active");
        document.body.classList.remove("no-scroll");
    });
}

if (openModalButton) {
    openModalButton.addEventListener("click", () => {
        modalOverlay.classList.add("modal--active");
        document.body.classList.add("no-scroll");
    });
}

const closeModal = (event) => {
    modalOverlay.classList.remove("modal--active");
    document.body.classList.remove("no-scroll");
};

if (modalOverlay) {
    modalOverlay.addEventListener("click", closeModal);
}

const modal = document.getElementById("modal");
const modalMessage = document.getElementById("modal-message");
const modalClose = document.getElementById("modal-close");
const modalSuccess = document.getElementById("modal-success");
const modalError = document.getElementById("modal-error");
const modalContent = document.querySelector(".modal-content");
if (modalSuccess) {
    modalSuccess.addEventListener("click", () => {
        showModal("Успешно отправлено!", "success");
    });
}
if (modalError) {
    modalError.addEventListener("click", () => {
        showModal("Sending error!", "error");
    });
}
// Показать модалку
function showModal(message, type) {
    modalMessage.textContent = message;

    if (type === "success") {
        modal.classList.remove("error");
        modal.classList.add("success");
        modalContent.style.backgroundColor = "#38b344";
    } else if (type === "error") {
        modal.classList.remove("success");
        modal.classList.add("error");
        modalContent.style.backgroundColor = "#b33838";
    }

    modal.classList.remove("hidden");
}

// Закрыть модалку
if (modalClose) {
    modalClose.addEventListener("click", () => {
        modal.classList.add("hidden");
    });
}
