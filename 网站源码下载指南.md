# CoreX MINER 网站源码下载与分析工具

## 🎯 功能概述

这套工具可以完整下载和分析网站源码，包括HTML、CSS、JavaScript、图片等所有资源，并提供详细的分析报告。

### ✨ 主要功能

- 🕷️ **完整源码下载**: 下载网站的所有HTML、CSS、JS、图片等资源
- 🔄 **资源关联处理**: 自动处理和下载页面中引用的所有资源
- 🌐 **离线浏览**: 创建可以离线浏览的网站副本
- 📊 **源码分析**: 提供详细的源码结构和内容分析
- 🔍 **安全检查**: 检测潜在的安全问题
- 📈 **性能评估**: 分析网站性能并提供优化建议

## 🚀 快速开始

### 1. 下载网站源码

```bash
# 下载默认页面集合
py download_source.py --confirm

# 下载单个页面及其资源
py download_source.py -s "https://app-core-x.online/account" --confirm

# 自定义输出目录
py download_source.py -o "my_website_backup" --confirm
```

### 2. 分析源码

```bash
# 分析下载的源码
py analyze_source.py <源码目录>

# 例如:
py analyze_source.py test_source
```

## 📁 工具文件结构

```
├── download_source.py     # 源码下载工具
├── source_downloader.py   # 下载器核心类
├── analyze_source.py      # 源码分析工具
└── requirements.txt       # 依赖包
```

## 📊 下载工具详解

### 主要参数

- `-u, --url`: 网站基础URL (默认: https://app-core-x.online)
- `-o, --output`: 输出目录名 (默认: corex_source)
- `-s, --single-page`: 下载单个页面及其资源
- `-p, --pages`: 要下载的页面URL列表
- `-v, --verbose`: 详细输出模式
- `-q, --quiet`: 静默模式
- `--confirm`: 确认开始下载（跳过警告）

### 输出目录结构

```
corex_source/
├── index.html          # 离线浏览索引
├── download_report.json # 下载报告
├── html/               # HTML页面
├── css/                # CSS样式文件
├── js/                 # JavaScript文件
├── images/             # 图片文件
├── fonts/              # 字体文件
└── assets/             # 其他资源
```

### 下载报告内容

下载报告(`download_report.json`)包含以下信息：

- 下载统计（总文件数、总大小等）
- 文件类型分布
- 所有下载的文件列表
- 失败的下载列表

## 🔍 分析工具详解

### 分析内容

- **HTML分析**: 页面结构、表单、链接、脚本等
- **CSS分析**: 样式表、选择器、属性、颜色等
- **JavaScript分析**: 脚本、函数、变量、事件等
- **图片分析**: 图片类型、大小分布等
- **结构分析**: 目录结构、文件类型分布等
- **安全分析**: 外部资源、内联脚本、表单安全等
- **性能分析**: 请求数、文件大小、优化建议等

### 分析报告

分析报告(`source_analysis_report.json`)包含详细的分析数据，可用于进一步研究。

## 🌐 离线浏览

下载完成后，可以通过以下方式浏览离线网站：

1. 打开输出目录中的`index.html`文件
2. 通过索引页面访问所有下载的HTML页面
3. 所有资源（CSS、JS、图片等）都已本地化，可完全离线浏览

## 🛠️ 高级用法

### 自定义页面列表

```bash
# 下载指定页面列表
py download_source.py -p "https://app-core-x.online/account" "https://app-core-x.online/account/tasks" --confirm
```

### 详细日志

```bash
# 显示详细日志
py download_source.py -v --confirm
```

### 静默模式

```bash
# 静默模式，只显示错误
py download_source.py -q --confirm
```

## ⚠️ 注意事项

### 法律和道德考虑

- ✅ **确保合规**: 检查网站robots.txt和服务条款
- ✅ **尊重服务器**: 控制请求频率，避免过大压力
- ✅ **数据保护**: 妥善处理下载的敏感信息
- ✅ **用途限制**: 仅用于学习、研究或授权用途

### 技术限制

- 🔄 **动态内容**: 无法下载JavaScript生成的动态内容
- 🔒 **登录保护**: 无法下载需要登录的受保护内容
- 🌐 **跨域资源**: 可能无法正确处理某些跨域资源
- 📱 **响应式设计**: 离线版可能无法完全模拟响应式行为

## 📋 使用示例

### 示例1: 下载主要页面

```bash
py download_source.py --confirm
```

这将下载以下页面及其资源：
- 主页面
- 账户页面
- 任务页面
- 支付页面
- 推荐页面
- 加速页面
- FAQ页面
- 支持页面
- 历史页面

### 示例2: 下载单个页面

```bash
py download_source.py -s "https://app-core-x.online/account/tasks" -o "tasks_page" --confirm
```

这将只下载任务页面及其资源，并保存到`tasks_page`目录。

### 示例3: 分析下载的源码

```bash
py analyze_source.py corex_source
```

这将分析`corex_source`目录中的源码，并生成详细报告。

## 🔄 故障排除

### 常见问题

1. **下载失败**
   - 检查网络连接
   - 确认URL是否正确
   - 查看是否需要登录
   - 尝试使用`-v`参数查看详细日志

2. **资源缺失**
   - 某些资源可能使用动态加载
   - 检查是否有跨域限制
   - 查看是否有反爬虫机制

3. **离线浏览问题**
   - 确保所有资源都已下载
   - 检查HTML中的路径是否正确
   - 某些功能可能依赖于服务器端处理

## 🎯 预期结果

使用这套工具，你可以：

1. **完整备份**: 获取网站的完整源码备份
2. **离线学习**: 在没有网络的情况下研究网站结构
3. **代码分析**: 深入了解网站的前端实现
4. **安全评估**: 检查潜在的安全问题
5. **性能优化**: 发现可能的性能优化点

---

**⚠️ 免责声明**: 本工具仅供学习和研究使用，使用者需确保遵守相关法律法规和网站服务条款。
