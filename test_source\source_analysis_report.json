{"analysis_time": "2025-07-31T09:46:21.546843", "source_directory": "test_source", "html_analysis": {"total_files": 1, "total_size": 29286, "pages": [{"file": "1392053_ca4cea3e6d22bad.html", "size": 29286, "title": "Account - Profile", "meta_description": "", "meta_keywords": "", "forms_count": 0, "links_count": 18, "images_count": 29, "scripts_count": 7, "stylesheets_count": 4}], "forms": [], "links": [{"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/faq", "text": "FAQ", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/support", "text": "Support", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/referals", "text": "Referral program", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/history", "text": "Transaction history", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up", "text": "", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up?unlock=24.93", "text": "UNLOCK", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up?unlock=99.93", "text": "UNLOCK", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up?unlock=499.93", "text": "UNLOCK", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up?unlock=999.93", "text": "UNLOCK", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up?unlock=2999.93", "text": "UNLOCK", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up", "text": "Power sale\n\n\t\t\t\t\t\tUP to 200% MORE POWER\t\t\t\t\t\t\n\n\n\nTime left:\n---", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up", "text": "Buy power in the store", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "https://t.me/share/url?url=https%3A%2F%2Ft.me%2Fcore_xbot%3Fstart%3D1392053&text=Cloud%20mining%20in%20just%201%20click%21%0AMine%20real%20USDT%20%E2%80%94%20withdraw%20easily%20anytime%21%0ADetails%20in%20the%20Bot%20%F0%9F%91%87%0A", "text": "Invite 3 friends\nReward: 450 GPU\n\n1/3", "is_external": true, "is_telegram": true}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/speed_up", "text": "Power", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/referals", "text": "<PERSON><PERSON><PERSON>", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account", "text": "Miner", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/payment", "text": "Withdraw", "is_external": false, "is_telegram": false}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/account/tasks", "text": "Tasks", "is_external": false, "is_telegram": false}], "meta_tags": [], "scripts": [{"file": "1392053_ca4cea3e6d22bad.html", "src": "https://code.jquery.com/jquery-3.6.0.min.js", "inline": false, "content_length": 0}, {"file": "1392053_ca4cea3e6d22bad.html", "src": "", "inline": true, "content_length": 526}, {"file": "1392053_ca4cea3e6d22bad.html", "src": "", "inline": true, "content_length": 8289}, {"file": "1392053_ca4cea3e6d22bad.html", "src": "", "inline": true, "content_length": 574}, {"file": "1392053_ca4cea3e6d22bad.html", "src": "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js", "inline": false, "content_length": 0}, {"file": "1392053_ca4cea3e6d22bad.html", "src": "", "inline": true, "content_length": 815}, {"file": "1392053_ca4cea3e6d22bad.html", "src": "/js/modal-info.js?7451055", "inline": false, "content_length": 0}], "stylesheets": [{"file": "1392053_ca4cea3e6d22bad.html", "href": "https://fonts.googleapis.com/css2?family=Lekton:ital,wght@0,400;0,700;1,400&family=Rubik:ital,wght@0,300..900;1,300..900&family=Unbounded:wght@200..900&display=swap", "media": "all"}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.1/normalize.min.css", "media": "all"}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "/styles/styles.css?1753926115", "media": "all"}, {"file": "1392053_ca4cea3e6d22bad.html", "href": "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css", "media": "all"}]}, "css_analysis": {"total_files": 1, "total_size": 69778, "files": [{"file": "styles.css", "size": 69778, "lines": 3229, "rules_count": 444}], "selectors": {":root": 1, "--main-color: #000;\n    --secondary-color: #fff;\n    --accent-color: #2cadef;\n    --warning-color: #d11b1be6;\n    --text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n}\n\n*": 1, "padding: 0;\n    margin: 0;\n    box-sizing: border-box;\n}\n\nbutton": 1, "background-color: transparent;\n    border: none;\n    cursor: pointer;\n}\nimg": 1, "display: block;\n}\na": 1, "text-decoration: none;\n    color: inherit;\n}\nhtml,\nbody": 1, "min-height: 100vh;\n    height: 100%;\n    margin: 0;\n    padding: 0;\n    /* background-image: url(/assets/red-bg.png); */\n    background-repeat: no-repeat;\n    background-position: bottom;\n    background-size: contain;\n    overflow: hidden;\n}\n.bg-none": 1, "background-image: none;\n}\nbody": 1, "/*background: linear-gradient(to bottom, #000000 70%, #800000 30%);*/\n    background-color: var(--main-color);\n    color: #fff;\n\n    font-family: \"Rubik\", sans-serif;\n}\n.no-scroll": 1, "overflow: hidden;\n}\n\n.red_text": 1, "color: red;\n}\n\n.green_text": 1, "color: lime;\n}\n\ninput[type=\"number\"]": 1, "/* Ð¡ÐºÑÑÑÑ ÐºÐ½Ð¾Ð¿ÐºÐ¸ Ñ Ð¸Ð½Ð¿ÑÑÐ° */\n    -moz-appearance: textfield; /* ÐÐ»Ñ Firefox */\n    -webkit-appearance: none; /* ÐÐ»Ñ WebKit-Ð±ÑÐ°ÑÐ·ÐµÑÐ¾Ð² (Chrome, Safari) */\n    appearance: none; /* ÐÐ»Ñ ÑÐ¾Ð²ÑÐµÐ¼ÐµÐ½Ð½ÑÑ Ð±ÑÐ°ÑÐ·ÐµÑÐ¾Ð² */\n}\n\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button": 1, "-webkit-appearance: none; /* ÐÐ»Ñ Chrome */\n    margin: 0; /* Ð£Ð±Ð¸ÑÐ°ÐµÐ¼ Ð»Ð¸ÑÐ½Ð¸Ðµ Ð¾ÑÑÑÑÐ¿Ñ */\n}\n\n.container": 1, "width: 100%;\n    max-width: 500px;\n    margin: 0 auto;\n    padding: 0 10px;\n    /* background-image: url(/assets/red-bg.png); */\n    background-repeat: no-repeat;\n    background-position: bottom;\n    background-size: contain;\n    /* min-height: 100vh; */\n}\n.wrapper": 1, "max-width: 500px;\n    margin: 0 auto;\n    overflow: hidden;\n    position: relative;\n}\n.scroll": 1, "/*flex: 1; !* ÐÐ°Ð½Ð¸Ð¼Ð°ÐµÑ Ð¾ÑÑÐ°Ð²ÑÐµÐµÑÑ Ð¿ÑÐ¾ÑÑÑÐ°Ð½ÑÑÐ²Ð¾ *!*/\n    overflow-y: auto; /* ÐÐºÐ»ÑÑÐ°ÐµÐ¼ Ð²ÐµÑÑÐ¸ÐºÐ°Ð»ÑÐ½ÑÑ Ð¿ÑÐ¾ÐºÑÑÑÐºÑ */\n    /*padding: 10px;*/\n    /*background: #f5f5f5;*/\n    max-height: calc(100vh - 50px); /* Ð£ÑÐ¸ÑÑÐ²Ð°ÐµÐ¼ Ð²ÑÑÐ¾ÑÑ Ð·Ð°Ð³Ð¾Ð»Ð¾Ð²ÐºÐ° */\n}\n/**\n  |============================\n  | modal\n  |============================\n*/\n.menu__box": 1, "position: relative;\n}\n.burger__list": 1, "display: flex;\n    flex-direction: column;\n    opacity: 0;\n    visibility: hidden;\n    transition: visibility 0.3s, opacity 0.3s ease;\n    position: absolute;\n    z-index: 10;\n    top: 40px;\n    left: -10px;\n    max-width: 120px;\n    border: 1px solid #333;\n    border-radius: 0px 0px 9px 0px;\n    background: #202020;\n    list-style: none;\n\n    font-size: 15px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.913px;\n}\n\n.burger__list.active": 1, "visibility: visible;\n    opacity: 1;\n    /* transition: visibility 0.3s, opacity 0.3s ease; */\n}\n\n.burger__list > li": 1, "border-top: 1px solid #333;\n    padding: 8px 10px;\n}\n.burger__list > li:nth-child(1)": 1, "border-top: none;\n    padding: 8px 10px;\n}\n.slider": 1, "position: relative;\n}\n\n/* Ð¡ÑÐ¸Ð»Ð¸ Ð´Ð»Ñ ÑÐµÐºÑÑÐ° Ð²Ð½ÑÑÑÐ¸ Ð¼Ð¾Ð´Ð°Ð»ÑÐ½Ð¾Ð³Ð¾ Ð¾ÐºÐ½Ð° */\n\n.modal-backdrop": 1, "position: fixed;\n\n    inset: 0;\n    opacity: 0;\n    z-index: 100;\n\n    visibility: hidden;\n    transition: opacity 0.2s ease, visibility 0.3s linear;\n}\n.modal__info": 1, "position: absolute;\n    width: calc(100% - 40px);\n    max-width: 320px;\n    top: 60px;\n    left: 50%;\n    transform: translateX(-50%);\n    height: 113px;\n    text-align: center;\n    display: inline-flex;\n    padding: 6px 13px 6px 11px;\n    justify-content: center;\n    align-items: center;\n    border-radius: 9px;\n    background: rgba(47, 47, 47, 0.7);\n    backdrop-filter: blur(12.5px);\n    -webkit-backdrop-filter: blur(10px);\n}\n.modal__info-power": 1, "top: 50%;\n    transform: translate(-50%, -50%);\n}\n/* ÐÐ¾Ð³Ð´Ð° Ð¼Ð¾Ð´Ð°Ð»ÑÐ½Ð¾Ðµ Ð¾ÐºÐ½Ð¾ Ð°ÐºÑÐ¸Ð²Ð½Ð¾ */\n.modal-backdrop.show": 1, "visibility: visible;\n    opacity: 1;\n}\n\n.modal__info--text": 1, "text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 15px;\n    font-weight: 400;\n    line-height: 18.01px; /* 138.537% */\n}\n.modal__info--text > span": 1, "display: block;\n    margin-top: 5px;\n    font-weight: 500;\n}\n/**\n  |============================\n  | exchange modal\n  |============================\n*/\n\n/* ÐÑÐ½Ð¾Ð²Ð½Ð¾Ð¹ Ð±Ð»Ð¾Ðº */\n.exchange-layout": 1, "opacity: 0;\n    visibility: hidden;\n    position: fixed;\n    z-index: 100;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    transition: opacity 0.4s ease, visibility 0.3s linear;\n    backdrop-filter: blur(8.5px);\n    -webkit-backdrop-filter: blur(10px);\n}\n\n/* ÐÐºÑÐ¸Ð²Ð°ÑÐ¸Ñ Ð¾ÐºÐ½Ð° */\n.exchange-layout.active": 1, "visibility: visible;\n    opacity: 1;\n}\n\n/* ÐÐ¾Ð½ÑÐµÐ¹Ð½ÐµÑ Ð´Ð»Ñ ÑÐµÐºÑÑÐ° */\n.exchange-modal": 1, "width: 318px;\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    display: flex;\n    flex-direction: column;\n    padding: 16px 20px 11px;\n    flex-shrink: 0;\n    border-radius: 8px;\n    background: rgba(47, 47, 47, 0.8);\n    box-shadow: 0px 3.425px 3.425px 0px rgba(0, 0, 0, 0.25);\n    backdrop-filter: blur(8.5px);\n    -webkit-backdrop-filter: blur(10px);\n    color: #fff;\n}\n.exchange-modal__title": 1, "margin-bottom: 16px;\n    font-size: 20px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 18.838px; /* 94.19% */\n}\n\n.exchange-modal__text": 1, "text-shadow: 0px 0.856px 0.856px rgba(0, 0, 0, 0.25);\n    font-size: 17px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 18.838px;\n    text-align: center;\n}\n\n.exchange-modal__actions": 1, "margin-top: 16px;\n    display: flex;\n    align-items: center;\n    justify-content: space-around;\n}\n\n/* ÐÐ±ÑÐ¸Ðµ ÑÑÐ¸Ð»Ð¸ ÐºÐ½Ð¾Ð¿Ð¾Ðº */\n.exchange-modal__button": 1, "width: 100px;\n    height: 25px;\n    border: none;\n    cursor: pointer;\n    border-radius: 6px;\n    background: var(--accent-color);\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);\n    color: #000;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n\n    font-size: 17px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 18.838px; /* 110.812% */\n    opacity: 0.7;\n}\n\n/* ÐÐ½Ð¸Ð¼Ð°ÑÐ¸Ñ Ð²ÑÐ°ÑÐµÐ½Ð¸Ñ Ð¸ÐºÐ¾Ð½ÐºÐ¸ */\n.exchange__icon.spin": 1, "animation: spin 0.6s ease;\n}\n\n@keyframes spin": 1, "from": 1, "transform: rotate(0deg);\n    }\n    to": 1, "transform: rotate(360deg);\n    }\n}\n/**\n  |============================\n  | header\n  |============================\n*/\n\n.header": 1, "height: 48px;\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    max-width: 500px;\n    width: 100%;\n    margin: 0 auto;\n    display: flex;\n    align-items: center;\n    background: #202020;\n    z-index: 100;\n}\n.header__nav": 1, "/* position: fixed;\n    top: 0; */\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    padding: 8px 0;\n}\n.header__menu-button": 1, "width: 28px;\n    height: 28px;\n}\n.header__menu-icon": 1, "width: 28px;\n    height: 28px;\n}\n.header__deposit-button": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 4px 10px;\n    margin-left: auto;\n\n    border-radius: 4px;\n\n    color: var(--main-color);\n    background: var(--accent-color);\n\n    font-family: \"Rubik\", sans-serif;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 600;\n}\n.header__user-info": 1, "display: flex;\n    align-items: center;\n    gap: 10px;\n}\n.header__balance": 1, "position: relative;\n    margin-left: auto;\n    height: 26px;\n    padding: 6px 13px 6px 30px;\n    display: flex;\n    align-items: center;\n    gap: 4px;\n    border-radius: 10px;\n    border: 1px solid #353535;\n    background: #131313;\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25) inset;\n}\n.header__balance > img": 1, "position: absolute;\n    top: 57%;\n    left: -7px;\n    transform: translateY(-50%);\n}\n.header__balance-amount": 1, "color: #fff;\n    font-size: 21px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.913px; /* 85.302% */\n}\n.header__user-plus": 1, "width: 32px;\n    height: 26px;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    border-radius: 5px;\n    background: #2cadef;\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.15);\n}\n.header__balance": 1, "font-size: 12px;\n    font-style: normal;\n    font-weight: 500;\n}\n.header__balance-label": 1, "}\n.header__balance-amount": 1, "}\n.header__avatar": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 4px;\n    padding: 1px;\n    background-color: var(--accent-color);\n}\n.header__avatar > img": 1, "width: 30px;\n    height: auto;\n}\n\n/**\n  |============================\n  | hero\n  |============================\n*/\n\n/* .main,\n.main-unlock": 1, "padding-bottom: 100px;\n    max-width: 500px;\n    margin: 0 auto;\n    min-height: calc(100vh - 48px);\n    background-image: url(/assets/hero-bg.png), url(/assets/red-bg.png);\n    background-repeat: no-repeat, no-repeat;\n    background-position: 0px -30px, bottom;\n    background-size: 100% 70%, contain;\n    transition: background-image 2s linear;\n    margin-top: 48px;\n}\n.main.changed-bg": 1, "background-image: url(/assets/hero-bg-two.png), url(/assets/red-bg.png);\n}\n@media screen and (min-width: 375px)": 1, ".main": 1, "background-position: 0px -26px, bottom;\n    }\n} */\n\n.main,\n.main-unlock": 1, "padding-bottom: 100px;\n    max-width: 500px;\n    margin: 0 auto;\n    min-height: calc(100vh - 48px);\n    background-image: url(/assets/red-bg1.png);\n    background-repeat: no-repeat;\n    background-position: bottom;\n    background-size: contain;\n    transition: background-image 2s linear;\n    margin-top: 46px;\n}\n\n.main-blur::before": 1, "content: \"\";\n    position: fixed;\n    z-index: 10;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    transition: opacity 0.4s ease, visibility 0.3s linear;\n    backdrop-filter: blur(3px);\n}\n\n.main-tasks,\n.main-chat,\n.main-power,\n.main-withdraw,\n.main-loading": 1, "padding-bottom: 100px;\n    max-width: 500px;\n    margin: 0 auto;\n    margin-top: 48px;\n    min-height: calc(100vh - 48px); /* 96px */\n    background-image: url(/assets/tasks/bg-tasks.png),\n        /* ÐÐµÑÑÐ½Ð¸Ð¹ ÑÐ¾Ð½ */ url(/assets/red-bg1.png); /* ÐÐ¸Ð¶Ð½Ð¸Ð¹ ÑÐ¾Ð½ */\n    background-repeat: no-repeat, no-repeat;\n    background-position: 0px -30px, bottom;\n    background-size: 100% 48%, contain;\n    transition: background-image 2s linear;\n}\n\n.main-chat": 1, "min-height: auto;\n\tpadding-bottom: 0px;\n\t\n}\n\n.main-power": 1, "margin-top: 0;\n}\n.main-loading": 1, "margin-top: 0;\n    padding-bottom: 0;\n    min-height: 100vh;\n}\n\n.container__loading": 1, "display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    gap: 7px;\n    min-height: 100vh;\n    text-align: center;\n    video": 1, "width: 94px;\n    }\n}\n.loading-text": 1, "font-size: 24px;\n    font-weight: 300;\n    display: flex;\n    align-items: center;\n    gap: 2px;\n    margin-left: 20px;\n}\n\n.loading-text span": 1, "opacity: 0;\n    animation: fadeIn 1.5s infinite;\n}\n\n.loading-text span:nth-child(1)": 1, "animation-delay: 0s;\n}\n\n.loading-text span:nth-child(2)": 1, "animation-delay: 0.3s;\n}\n\n.loading-text span:nth-child(3)": 1, "animation-delay: 0.6s;\n}\n\n@keyframes fadeIn": 1, "0%,\n    100%": 1, "opacity: 0;\n    }\n    50%": 1, "opacity: 1;\n    }\n}\n.hero": 1, "padding-top: 0;\n    height: 250px;\n    padding-bottom: 12px;\n    /* display: none; */\n    transition: opacity 0.3s ease;\n}\n.hero-unlock": 1, "padding-top: 18px;\n}\n.hero.active": 1, "display: block;\n}\n.hero__container": 1, "position: relative;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n}\n.hero__status": 1, "background-image: url(/assets/hero/bg.png);\n    background-position: 0 2px;\n    background-size: cover;\n    background-repeat: no-repeat;\n    text-transform: uppercase;\n    position: relative;\n    z-index: 11;\n    margin: 0 auto;\n    display: flex;\n    flex-direction: column;\n    width: 215px;\n    height: 76px;\n    padding: 8px 5px 10px;\n    gap: 4px;\n    border-radius: 11px;\n    border-top: none;\n    text-align: center;\n}\n.hero__status--next": 1, "position: relative;\n    text-align: center;\n    margin-bottom: 11px;\n}\n.hero__status-text": 1, "color: #2cadef;\n    text-shadow: 0px 2.013px 2.013px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 18.034px; /* 100.191% */\n}\n.hero__status-text.active": 1, "margin-top: 4px;\n    margin-bottom: 5px;\n    text-shadow: 0px 2.013px 2.013px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 18.034px;\n    color: rgba(255, 255, 255, 0.9);\n}\n.hero__status-text-next": 1, "font-size: 20px;\n    font-style: normal;\n    font-weight: 600;\n    margin-bottom: 2px;\n}\n.hero__start-button": 1, "font-size: 45px;\n    font-weight: 600;\n    line-height: 80%;\n    color: #fff;\n}\n\n.hero__timer": 1, "color: rgba(255, 255, 255, 0.6);\n    text-align: center;\n    text-shadow: 0px 2.013px 2.013px rgba(0, 0, 0, 0.25);\n    font-family: \"Unbounded\";\n    font-size: 30px;\n    font-style: normal;\n    font-weight: 200;\n    line-height: 18.034px; /* 60.115% */\n}\n\n.hero__start-button-next": 1, "color: #fff;\n    text-align: center;\n    font-size: 12px;\n    font-style: normal;\n    font-weight: 400;\n    opacity: 0.7;\n}\n\n.hero__info-button": 1, "position: absolute;\n    top: 3px;\n    right: -55px;\n}\n.hero__info-button > img": 1, "width: 30px;\n}\n.hero__box": 1, "height: 122px;\n}\n.hero__image-wrapper": 1, "cursor: pointer;\n}\n.hero__image-wrapper--next": 1, "margin-top: 0;\n    margin-right: 0;\n}\n.hero__image": 1, "width: 210px;\n    height: auto;\n    margin-right: -10px;\n}\n\n.hero__arrow-button": 1, "display: none;\n    right: 15px;\n    top: 50%;\n    position: absolute;\n}\n.custom-button-prev,\n.custom-button-next": 1, "position: absolute;\n    top: 50%;\n    transform: translateY(-50%);\n    z-index: 10;\n    color: #fff;\n    border: none;\n    padding: 10px 15px;\n    cursor: pointer;\n}\n\n.custom-button-prev": 1, "left: 10px;\n}\n\n.custom-button-next": 1, "right: 10px;\n}\n\n.hero__arrow-button--left": 1, "left: 15px;\n    top: 50%;\n    position: absolute;\n}\n.hero__image-wrapper .hero__arrow-button--left.active,\n.hero__image-wrapper .hero__arrow-button--right.active": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n\n    width: 30px;\n    height: 30px;\n}\n.hero__arrow-icon": 1, "z-index: 1;\n}\n\n.swiperZ": 1, "z-index: 12;\n}\n\n.hero__hashes": 1, "margin: 0 auto;\n    /* width: 171px; */\n    padding: 4px 8px 2px;\n    border-radius: 4px;\n    background: rgba(47, 47, 47, 0.4);\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.15);\n}\n.unlock": 1, "margin-top: 10px;\n    width: 135px;\n    text-align: center;\n    font-size: 12px;\n    font-style: normal;\n    font-weight: 500;\n    background: #fff;\n    color: #000;\n}\n.hero__hashes-count": 1, "display: flex;\n    align-items: center;\n    gap: 10px;\n    font-family: \"Lekton\", monospace;\n    font-weight: 700;\n    font-size: 24px;\n    text-shadow: var(--text-shadow);\n}\n.hero__hashes-unit": 1, "font-family: \"Rubik\", sans-serif;\n\n    color: #aaa;\n    font-size: 18px;\n    font-weight: 400;\n    font-style: normal;\n}\n\n/**\n  |============================\n  | power secstion\n  |============================\n*/\n\n.power__content": 1, "margin-bottom: 6px;\n    padding: 8px 12px 12px;\n    border-radius: 14px;\n    background: rgba(47, 47, 47, 0.7);\n    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);\n    font-weight: 500;\n    line-height: 17.9px;\n}\n.power__label": 1, "margin-bottom: 6px;\n    color: rgba(255, 255, 255, 0.7);\n    text-shadow: var(--text-shadow);\n    font-size: 16px;\n}\n.power__amount": 1, "margin-bottom: 12px;\n    text-shadow: var(--text-shadow);\n    font-size: 24px;\n}\n.power__link": 1, "display: flex;\n    align-items: center;\n    height: 34px;\n    gap: 8px;\n    background-color: var(--accent-color);\n    padding: 8px 9px;\n    border-radius: 10px;\n    border: 1px solid #4ec3ff;\n\n    color: #fff;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n    font-size: 22px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px; /* 81.425% */\n}\n.power__icon": 1, "width: 50px;\n}\n/**\n  |============================\n  | exchange section\n  |============================\n*/\n\n.exchange__note": 1, "margin-bottom: 4px;\n    color: rgba(255, 255, 255, 0.7);\n    text-align: center;\n    font-size: 14px;\n    font-weight: 300;\n    line-height: 17.913px; /* 162.85% */\n}\n.exchange__content": 1, "margin-bottom: 6px;\n    padding: 0 8px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    text-align: center;\n}\n.exchange__mined": 1, "}\n.exchange__label": 1, "color: rgba(255, 255, 255, 0.7);\n    font-size: 16px;\n    font-weight: 500;\n\t\n}\n.exchange__amount": 1, "font-size: 24px;\n    font-weight: 500;\n    color: #fff;\n}\n.exchange__icon": 1, "width: 24px;\n}\n.exchange__button": 1, "display: flex;\n    align-items: center;\n    gap: 11px;\n    padding: 8px 16px 8px 25px;\n    border-radius: 9px;\n    border: 1px solid #4ec3ff;\n    background: var(--accent-color);\n    color: #fff;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px; /* 99.519% */\n}\n\n/**\n  |============================\n  | invite section\n  |============================\n*/\n\n.invite": 1, "}\n\n.invite__container": 1, "display: flex;\n    align-items: center;\n    padding: 6px 12px 6px 6px;\n    border-radius: 13px;\n    background: rgba(47, 47, 47, 0.7);\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);\n}\n.invite__container--start": 1, "margin-bottom: 5px;\n    position: relative;\n    /* z-index: 11; */\n\n    .invite__image": 1, "width: 42px;\n    }\n\n    .invite__image-wrapper": 1, "background-color: transparent;\n    }\n}\n.invite__image-wrapper": 1, "width: 44px;\n    height: 42px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 6px 8px 6px 7px;\n    margin-right: 8px;\n    border-radius: 9px;\n    background: var(--accent-color);\n    border: 1px solid #4ec3ff;\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);\n}\n.invite__image-wrapper--plane": 1, "padding-bottom: 0 !important;\n}\n.invite__image": 1, "/* margin-bottom: -5px; */\n    width: 32px;\n    height: auto;\n}\n.invite__details": 1, "}\n.invite__text": 1, "font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    text-shadow: var(--text-shadow);\n}\n.invite__reward": 1, "color: rgba(255, 255, 255, 0.7);\n    text-shadow: var(--text-shadow);\n    font-size: 12px;\n    font-style: normal;\n    font-weight: 400;\n}\n.invite__progress": 1, "padding: 5px 8px;\n    border-radius: 5px;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 400;\n\n    background: var(--accent-color);\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.15);\n    margin-left: auto;\n}\n\n/**\n  |============================\n  | footer\n  |============================\n*/\n\n.footer": 1, "position: fixed;\n    z-index: 9;\n    bottom: 23px;\n    left: 0;\n    right: 0;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: 500px;\n    width: 100%;\n}\n.footer__nav": 1, "display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    padding: 10px;\n\n    border-radius: 26px;\n    background: rgba(47, 47, 47, 0.5);\n    -webkit-backdrop-filter: blur(10px);\n    backdrop-filter: blur(10px);\n}\n.footer__nav": 1, "background: rgba(47, 47, 47, 0.8); /* Ð¤Ð¾Ð»Ð»Ð±ÐµÐº */\n}\n\n@supports (backdrop-filter: blur(10px))": 1, ".footer__nav": 1, "backdrop-filter: blur(10px);\n        -webkit-backdrop-filter: blur(10px);\n        background: rgba(47, 47, 47, 0.5);\n    }\n}\n\n.footer__list": 1, "display: flex;\n    align-items: center;\n    list-style: none;\n    gap: 8px;\n    width: 100%;\n}\n.footer__item": 1, "width: calc((100% - 8px * 4) / 4);\n}\n.footer__link": 1, "display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    opacity: 0.5;\n}\n.footer__icon": 1, "width: 25px;\n    margin-bottom: 2px;\n}\n\n.footer__text": 1, "color: #fff;\n\n    font-size: 10px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.913px; /* 223.919% */\n}\n\n.footer__link--active": 1, "opacity: 1;\n    transform: scale(1.15);\n}\n\n/**\n  |============================\n  | sale\n  |============================\n*/\n\n.sale": 1, "padding:0 8px;\n    margin-bottom: 6px;\n    border-radius: 11px;\n    background: rgba(121, 222, 131, 0.7);\n}\n\n.sale__link": 1, "height: 47px;\n    display: flex;\n    align-items: center;\n}\n.sale__info": 1, "}\n.sale__title": 1, "font-size: 21px;\n    font-style: normal;\n    font-weight: 600;\n    line-height: 17.913px;\n}\n.sale__offer": 1, "display: flex;\n    align-items: center;\n    gap: 5px;\n    font-size: 17px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px;\n}\n.sale__icon": 1, "width: 20px;\n}\n.sale__timer": 1, "margin-left: auto;\n}\n.sale__timer-title": 1, "font-size: 15px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px;\n}\n.sale__time": 1, "font-size: 12px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.913px;\n}\n\n\n\n\n\n.sale2": 1, "padding:0 8px;\n    margin-bottom: 6px;\n    border-radius: 11px;\n    background: #2CADEF;\n\tborder: 1px solid #A0DFFF;\n}\n\n.sale2__link": 1, "height: 47px;\n    display: flex;\n    align-items: center;\n}\n.sale2__info": 1, "}\n.sale2__title": 1, "font-size: 21px;\n    font-style: normal;\n    font-weight: 600;\n    line-height: 17.913px;\n}\n.sale2__offer": 1, "display: flex;\n    align-items: center;\n    gap: 5px;\n    font-size: 17px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px;\n}\n.sale2__timer": 1, "margin-left: auto;\n}\n.sale2__timer-title": 1, "font-size: 15px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px;\n}\n.sale2__time": 1, "font-size: 12px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.913px;\n}\n\n\n\n\n\n\n/**\n  |============================\n  | tasks\n  |============================\n*/\n\n.tasks-invite": 1, "padding-top: 12px;\n}\n.tasks-invite__container": 1, "display: flex;\n    align-items: center;\n    padding: 5px 12px 5px 6px;\n    border-radius: 13px;\n    background: rgba(47, 47, 47, 0.7);\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);\n}\n.tasks-invite__list": 1, "display: flex;\n    flex-direction: column;\n    list-style: none;\n    gap: 8px;\n}\n\n.tasks-invite__title": 1, "color: #fff;\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 30px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 32px; /* 85.094% */\n}\n.tasks-invite__subtitle": 1, "margin-top: 12px;\n    margin-bottom: 13px;\n    color: rgba(255, 255, 255, 0.7);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.551px; /* 97.503% */\n}\n.tasks-invite__link": 1, "}\n.tasks-invite__image-wrapper": 1, "width: 44px;\n    height: 42px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 6px 8px 6px 7px;\n    margin-right: 8px;\n    border-radius: 11px;\n    background: #2cadef;\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);\n}\n\n.tasks-invite__image-wrapper--other": 1, "background: #393939;\n    padding: 9px 8px 4px 7px;\n}\n.tasks-invite__image-wrapper--other > .tasks-invite__image": 1, "width: 38px;\n}\n.tasks-invite__image": 1, "width: 32px;\n    height: auto;\n}\n.tasks-invite__details": 1, "margin-top: -6px;\n    position: relative;\n    width: 90%;\n}\n.tasks-invite__text": 1, "font-size: 14px;\n    font-style: normal;\n    font-weight: 500;\n    text-shadow: var(--text-shadow);\n}\n.tasks-invite__reward": 1, "color: rgba(255, 255, 255, 0.7);\n    text-shadow: var(--text-shadow);\n    font-size: 10px;\n    font-style: normal;\n    font-weight: 400;\n    margin-bottom: 8px;\n}\n.tasks-invite__progress": 1, "padding: 5px 8px;\n    border-radius: 5px;\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 400;\n\n    background: var(--accent-color);\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.15);\n    margin-left: auto;\n}\n.tasks-invite__progress--line": 1, "position: absolute;\n    left: 0;\n    bottom: -2px;\n    width: 95%;\n    height: 6px;\n    border-radius: 5px;\n    background: rgba(255, 255, 255, 0.5);\n}\n.tasks-invite__progress--line::before": 1, "content: \"\";\n    position: absolute;\n    width: 40%;\n    height: 6px;\n    border-radius: 10px;\n    background: rgba(255, 255, 255, 0.8);\n}\n@media screen and (min-width: 425px)": 1, ".tasks-invite__title": 1, "font-size: 37px;\n    }\n    .tasks-invite__subtitle": 1, "font-size: 22px;\n    }\n    .tasks-invite__text": 1, "font-size: 14px;\n    }\n    .tasks-invite__reward": 1, "font-size: 11px;\n    }\n    .tasks-invite__progress": 1, "font-size: 16px;\n    }\n}\n\n/**\n  |============================\n  | power-shop\n  |============================\n*/\n\n.power-shop": 1, "margin-bottom: 12px;\n}\n.container": 1, "}\n.power-shop__container": 1, "display: flex;\n    flex-direction: column;\n    align-items: center;\n}\n.power-shop__title": 1, "margin-bottom: 9px;\n    color: #fff;\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 30px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.705px; /* 59.015% */\n}\n.power-shop__description": 1, "margin-bottom: 9px;\n    max-width: 176px;\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.705px;\n}\n.power-shop__input-wrapper": 1, "margin-bottom: 10px;\n}\n.power-shop__input": 1, "width: 94px;\n    padding: 4.829px 0px 3.171px 5px;\n    border-radius: 5px;\n    background: rgba(255, 255, 255, 0.6);\n    color: #000;\n    text-align: center;\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 17.705px; /* 98.359% */\n}\n.power-shop__input::placeholder": 1, "color: #000;\n    text-align: center;\n    padding-left: 10px;\n}\n\n.power-shop__currency": 1, "font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.705px;\n}\n.power-shop__calculator": 1, "width: 100%;\n    border-radius: 8.048px;\n    background: rgba(47, 47, 47, 0.7);\n    padding: 12px 10px;\n}\n.power-shop__calculator-title": 1, "margin-bottom: 3px;\n    color: #fff;\n    text-align: center;\n    font-size: 14px;\n    font-weight: 500;\n    line-height: 17.705px; /* 126.461% */\n}\n.power-shop__details": 1, "display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n.power-shop__detail": 1, "display: flex;\n    align-items: center;\n\n    color: rgba(255, 255, 255, 0.8);\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 20px; /* 142.857% */\n}\n.power-shop__value": 1, "color: var(--accent-color);\n    margin-left: auto;\n}\n.power-shop__pay-button": 1, "color: #fff;\n    width: 100%;\n    height: 39px;\n    padding: 4px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 5px;\n    background: var(--accent-color);\n    box-shadow: 0px 1.61px 1.61px 0px rgba(0, 0, 0, 0.25);\n\n    font-size: 21px;\n    font-weight: 500;\n}\n.power-shop__info": 1, "top: 10px;\n    left: 50%;\n    transform: translateX(-50%);\n}\n\n.power-sale": 1, "margin-top: 48px;\n}\n.power-sale__container": 1, "padding: 10px 10px 6px;\n    background: #549b5c;\n    text-align: center;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 22px;\n}\n.power-sale__title": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 4px;\n\n    color: #fff;\n    text-align: center;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.705px; /* 98.359% */\n}\n.power-sale__icon": 1, "width: 18px;\n}\n.power-sale__offer": 1, "}\n.power-sale__bonus": 1, "font-weight: 500;\n}\n.power-sale__time": 1, "margin-top: 4px;\n}\n.power-sale__time-label": 1, "font-size: 16px;\n    font-weight: 500;\n}\n.power-sale__time-value": 1, "}\n\n/**\n  |============================\n  | purchase\n  |============================\n*/\n.purchase": 1, "}\n.purchase__container": 1, "padding-top: 18px;\n}\n.purchase__label": 1, "color: #fff;\n    text-align: center;\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 24px; /* 133.333% */\n}\n.purchase__label--two": 1, "margin-bottom: 10px;\n}\n.purchase__amount": 1, "text-align: center;\n    font-size: 20px;\n    font-style: normal;\n    font-weight: 700;\n    line-height: 24px;\n}\n.purchase__change-amount": 1, "display: block;\n    color: #2aa3ff;\n    text-align: center;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 24px; /* 171.429% */\n}\n.purchase__methods": 1, "display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n}\n.purchase__method": 1, "-ms-flex-preferred-size: calc((100% - 8px) / 2);\n    flex-basis: calc((100% - 8px) / 2);\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    gap: 5px;\n    padding: 2px 3px;\n    height: 57px;\n    border-radius: 27px 10px 10px 27px;\n    background: rgba(255, 255, 255, 0.16);\n}\n\n.purchase__method:nth-child(6n)": 1, "margin-bottom: 20px;\n}\n\n.purchase__method:nth-child(2n)": 1, "border-radius: 10px 27px 27px 10px;\n    background: rgba(255, 255, 255, 0.16);\n    -webkit-box-pack: end;\n    -ms-flex-pack: end;\n    justify-content: end;\n    text-align: end;\n}\n.purchase__link": 1, "/* width: 100%; */\n    display: flex;\n    align-items: center;\n    gap: 5px;\n}\n\n/* .purchase__method:nth-child(2n) > .purchase__link": 1, "justify-content: end;\n} */\n\n.purchase__link > div": 1, "display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n.purchase__icon": 1, "width: 50px;\n}\n.purchase__name": 1, "text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 13px; /* 72.222% */\n}\n.purchase__details": 1, "color: rgba(255, 255, 255, 0.8);\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 300;\n    line-height: 16px;\n}\n\n/**\n  |============================\n  | top-up\n  |============================\n*/\n.top-up__container": 1, "padding-top: 18px;\n}\n.top-up__title": 1, "margin-bottom: 7px;\n    color: #fff;\n    text-align: center;\n    font-size: 30px;\n    font-weight: 700;\n    line-height: 24px; /* 80% */\n}\n.top-up__change-method": 1, "display: block;\n    margin-bottom: 4px;\n    color: #2aa3ff;\n    text-align: center;\n    font-size: 14px;\n    font-weight: 300;\n    line-height: 24px; /* 171.429% */\n}\n.top-up__description": 1, "margin-bottom: 4px;\n    color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 18px; /* 112.5% */\n}\n.top-up__form": 1, "display: flex;\n    flex-direction: column;\n    gap: 12px;\n    padding: 10px 8px;\n    margin-bottom: 8px;\n    border-radius: 11px;\n    background: rgba(47, 47, 47, 0.7);\n}\n.top-up__field": 1, "}\n.top-up__label": 1, "margin-bottom: 7px;\n    color: #fff;\n    font-size: 14px;\n    font-weight: 400;\n    line-height: 18px; /* 128.571% */\n}\n.top-up__input-label": 1, "margin-bottom: 9px;\n    position: relative;\n    display: flex;\n    flex-direction: column;\n}\n.top-up__input": 1, "height: 32px;\n    padding: 6px 5px 6px 9px;\n    border-radius: 5px;\n    background: rgba(128, 128, 128, 0.3);\n    border: none;\n    outline: none;\n\n    color: rgba(255, 255, 255, 0.8);\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 18px; /* 128.571% */\n}\n.top-up__icon": 1, "position: absolute;\n    right: 4px;\n    top: 7px;\n    z-index: 2;\n    width: 20px;\n    cursor: pointer;\n}\n.top-up__button": 1, "width: 100%;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 7px;\n    background: var(--accent-color);\n    opacity: 0.8;\n\n    color: #fff;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n    font-family: \"Rubik\";\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 24.429px; /* 187.912% */\n}\n.top-up__info": 1, "}\n.top-up__info-text": 1, "margin-bottom: 8px;\n    border-radius: 9px;\n    background: #579d5c;\n    padding: 3px 8px 2px 9px;\n\n    color: #fff;\n    text-align: center;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 16px; /* 123.077% */\n}\n.top-up__info-description": 1, "width: 83%;\n    margin: 0 auto;\n    color: rgba(255, 255, 255, 0.7);\n    text-align: center;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 18px; /* 128.571% */\n}\n\n/**\n  |============================\n  | earn - referrals\n  |============================\n*/\n\n.referrals__container": 1, "padding-top: 18px;\n    margin-bottom: 13px;\n}\n\n.referrals__title": 1, "margin-bottom: 7px;\n    color: #fff;\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 30px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.551px; /* 58.502% */\n}\n.referrals__subtitle": 1, "margin-bottom: 15px;\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.551px; /* 125.361% */\n}\n.referrals__link-section": 1, "margin-bottom: 12px;\n    padding: 10px 10px 8px;\n    border-radius: 8px;\n    background: rgba(47, 47, 47, 0.7);\n}\n.referrals__label": 1, "margin-bottom: 4px;\n    color: #fff;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.551px; /* 109.691% */\n}\n.referrals__link-wrapper": 1, "margin-bottom: 7px;\n    position: relative;\n    display: flex;\n    align-items: center;\n    padding-left: 7px;\n    padding-right: 4px;\n\n    height: 32px;\n    border-radius: 9px;\n    background: rgba(128, 128, 128, 0.3);\n}\n.referrals__link": 1, "color: rgba(255, 255, 255, 0.9);\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n    /* font-family: \"Hind\"; */\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.551px; /* 109.691% */\n}\n.referrals__copy-button": 1, "margin-left: auto;\n}\n.referrals__qr-code": 1, "width: 24px;\n}\n.referrals__share-button,\n.referrals__rewards-button": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    height: 32px;\n    width: 100%;\n    border-radius: 7px;\n    background: #2cadef;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n\n    color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.551px; /* 109.691% */\n}\n.referrals__share-icon": 1, "width: 19px;\n}\n.referrals__info": 1, "padding: 8px 10px;\n    border-radius: 11px;\n    background: rgba(47, 47, 47, 0.7);\n}\n.referrals__info-title": 1, "margin-bottom: 8px;\n    color: #fff;\n    text-align: center;\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 17.913px; /* 99.519% */\n}\n.referrals__info-text": 1, "width: 90%;\n    margin-left: auto;\n    margin-right: auto;\n    margin-bottom: 8px;\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    font-size: 14px;\n    font-weight: 400;\n    line-height: 1.2; /* 109.091% */\n\t\n}\n.referrals__info-text:nth-child(3)": 1, "width: 60%;\n}\n.referrals__highlight": 1, "color: rgba(255, 255, 255, 0.9);\n    font-size: 11px;\n    font-weight: 500;\n    line-height: 12px;\n}\n.referrals__rewards-button": 1, "}\n.referrals__rewards-icon": 1, "width: 19px;\n}\n.referrals-list": 1, "}\n.referrals-list__container": 1, "}\n.referrals-list__title": 1, "margin-bottom: 13px;\n    color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-weight: 500;\n    line-height: 17.913px; /* 111.959% */\n}\n.referrals-list__levels": 1, "margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n    gap: 7px;\n}\n.referrals-list__level": 1, "padding-top: 4px;\n    padding-bottom: 2px;\n    width: calc((100% - 14px) / 3);\n    border-radius: 5px;\n    background: rgba(47, 47, 47, 0.7);\n\n    text-align: center;\n}\n.referrals-list__level-title": 1, "color: #fff;\n    font-size: 11px;\n    font-style: normal;\n    font-weight: 400;\n}\n.referrals-list__count": 1, "font-size: 13px;\n    font-weight: 500;\n    line-height: 17.913px;\n}\n.referrals-list__details": 1, "max-width: 380px;\n    margin: 0 auto;\n}\n.referrals-list__column": 1, "display: flex;\n    align-items: center;\n    justify-content: space-between;\n}\n.referrals-list__header": 1, "margin-bottom: 15px;\n    color: #fff;\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 17.551px; /* 97.503% */\n}\n.referrals-list__data": 1, "display: flex;\n    align-items: center;\n    gap: 20px;\n    justify-content: space-between;\n    text-align: center;\n    opacity: 0.8;\n    color: #fff;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.551px; /* 125.361% */\n}\n\n\n.referrals-list__data + .referrals-list__data": 1, "margin-top: 15px;\n}\n\n.pagination": 1, "}\n.pagination__container": 1, "margin-top: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n}\n\n.pagination__button": 1, "height: 25px;\n    padding: 3px 7px 2px 7px;\n    border-radius: 100px;\n    background: rgba(47, 47, 47, 0.6);\n}\n.pagination__button--prev": 1, "}\n.pagination__icon": 1, "width: 23px;\n}\n.pagination__info": 1, "color: #fff;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.551px; /* 125.361% */\n}\n.pagination__button--next": 1, "}\n\n.modal__overlay": 1, "position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    z-index: 1000;\n    backdrop-filter: blur(8.5px);\n    -webkit-backdrop-filter: blur(10px);\n    display: none;\n}\n\n/* ÐÐ¾Ð½ÑÐµÐ½Ñ Ð¼Ð¾Ð´Ð°Ð»ÑÐ½Ð¾Ð³Ð¾ Ð¾ÐºÐ½Ð° */\n.modal__content": 1, "position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -36%);\n    width: 300px;\n    padding: 8px 10px;\n    border-radius: 11px;\n    background: rgba(47, 47, 47, 0.8);\n    box-shadow: 0px 3.425px 3.425px 0px rgba(0, 0, 0, 0.25);\n    backdrop-filter: blur(8.5px);\n    -webkit-backdrop-filter: blur(10px);\n    z-index: 1001;\n}\n@media screen and (min-width: 375px)": 1, ".modal__content": 2, "transform: translate(-50%, -50%);\n\n        width: 360px;\n    }\n}\n@media screen and (min-width: 425px)": 1, "transform: translate(-50%, -50%);\n\n        width: 410px;\n    }\n}\n.modal__text": 1, "margin-bottom: 15px;\n    color: #fff;\n    text-align: center;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.551px; /* 125.361% */\n}\n.modal__text:last-child": 1, "margin-bottom: 0;\n    font-size: 10px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 14px;\n}\n.modal__overlay.modal--active": 1, "display: block;\n}\n\n/**\n  |============================\n  | withdraw\n  |============================\n*/\n\n.main-withdraw": 1, "margin-top: 0;\n}\n\n.withdraw__container": 1, "padding-top: 18px;\n}\n\n.withdraw__title": 1, "margin-bottom: 20px;\n    color: #fff;\n    text-align: center;\n    font-size: 30px;\n    font-weight: 500;\n    line-height: 17.913px; /* 59.712% */\n}\n.withdraw__subtitle": 1, "margin-bottom: 14px;\n    color: #fff;\n    text-align: center;\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px; /* 137.796% */\n}\n.withdraw__methods": 1, "display: flex;\n    flex-wrap: wrap;\n    column-gap: 8px;\n    row-gap: 28px;\n}\n.withdraw__method": 1, "position: relative;\n    flex-basis: calc((100% - 8px) / 2);\n    display: flex;\n    align-items: center;\n    gap: 5px;\n    padding: 2px 3px;\n    height: 57px;\n    border-radius: 27px 10px 10px 27px;\n    background: rgba(255, 255, 255, 0.16);\n}\n\n.withdraw__method:nth-child(2n)": 1, "border-radius: 10px 27px 27px 10px;\n    background: rgba(255, 255, 255, 0.16);\n    justify-content: end;\n    text-align: end;\n}\n\n.withdraw__min": 1, "position: absolute;\n    bottom: -20px;\n    left: 50%;\n    transform: translateX(-50%);\n    font-size: 10px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 13px; /* 130% */\n}\n.withdraw__link": 1, "display: flex;\n    align-items: center;\n    gap: 5px;\n}\n.withdraw__link > div": 1, "display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n.withdraw__icon": 1, "width: 50px;\n}\n\n.withdraw__name": 1, "text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 13px; /* 72.222% */\n}\n.withdraw__details": 1, "color: rgba(255, 255, 255, 0.8);\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 300;\n    line-height: 16px;\n}\n\n.details__bonus": 1, "margin-top: 47px;\n    padding: 6px 50px;\n    height: 65px;\n    background: #54819b;\n}\n\n.details__bonus-text": 1, "color: #fff;\n    text-align: center;\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 13px; /* 100% */\n}\n.details__bonus-link": 1, "margin: 0 auto;\n    margin-top: 4px;\n    max-width: 200px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 2px 0;\n    border-radius: 4.885px;\n    background: #fff;\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.1);\n\n    color: #000;\n    text-align: center;\n    font-size: 11px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px; /* 162.85% */\n}\n\n/**\n  |============================\n  | PAYMENT\n  |============================\n*/\n\n.payment-balance__container": 1, "padding-top: 18px;\n}\n\n.payment-balance__title,\n.payment-balance__confirm-text": 1, "margin-bottom: 6px;\n    color: #fff;\n    text-align: center;\n    font-size: 22px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 24px; /* 109.091% */\n}\n.payment-balance__description": 1, "max-width: 310px;\n    margin: 0 auto;\n    margin-bottom: 22px;\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 20px; /* 142.857% */\n}\n.payment-balance__confirm-text": 1, "margin-bottom: 22px;\n}\n.payment-balance__details": 1, "color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 13px; /* 81.25% */\n}\n.payment-balance__balance": 1, "margin-bottom: 13px;\n}\n.payment-balance__purchase": 1, "margin-bottom: 18px;\n}\n.payment-balance__actions": 1, "margin: 0 auto;\n}\n.payment-balance__button": 1, "display: inline-flex;\n    padding: 11px 15px 11px 16px;\n    justify-content: center;\n    align-items: center;\n    border-radius: 4px;\n\n    color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 13px; /* 81.25% */\n}\n.payment-balance__button--confirm": 1, "background: #549b5c;\n}\n.payment-balance__button--cancel": 1, "margin-left: 50px;\n    background: #9e4636;\n}\n\n/**\n  |============================\n  | withdraw-next\n  |============================\n*/\n\n.withdraw-next__container": 1, "padding-top: 18px;\n}\n\n.withdraw-next__title": 1, "margin-bottom: 11px;\n    color: #fff;\n    text-align: center;\n    font-size: 30px;\n    font-weight: 500;\n    line-height: 17.913px; /* 59.712% */\n}\n.withdraw-next__link": 1, "margin-bottom: 4px;\n    display: block;\n    color: #2aa3ff;\n    text-align: center;\n    font-size: 14px;\n    font-weight: 400;\n    line-height: 24px; /* 171.429% */\n}\n.withdraw-next__form": 1, "display: flex;\n    flex-direction: column;\n    gap: 10px;\n\n    padding: 8px 10px 15px;\n    border-radius: 8px 8px 0px 0px;\n    background: rgba(47, 47, 47, 0.7);\n\n    color: #fff;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 24px; /* 171.429% */\n}\n.withdraw-next__label": 1, "display: flex;\n    flex-direction: column;\n    gap: 5px;\n    text-align: center;\n}\n.withdraw-next__input": 1, "padding-left: 6px;\n    border: none;\n    outline: none;\n    height: 37px;\n    text-align: center;\n    color: #fff;\n    border-radius: 6px;\n    background: rgba(128, 128, 128, 0.5);\n}\n\n.withdraw-next__button": 1, "margin-bottom: 11px;\n    width: 100%;\n    height: 36px;\n    padding: 11px;\n    border-radius: 0px 0px 6px 6px;\n    background: var(--accent-color);\n    color: #fff;\n    font-size: 20px;\n    font-weight: 500;\n    line-height: 14px; /* 70% */\n}\n.withdraw-next__button--confirm": 1, "}\n.withdraw-next__image": 1, "width: 70px;\n    margin-bottom: 6px;\n    margin-left: auto;\n    margin-right: auto;\n}\n.withdraw-next__fee": 1, "margin-bottom: 3px;\n    color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-weight: 500;\n    line-height: 24px;\n}\n.withdraw-next__note": 1, "width: 300px;\n    margin: 0 auto;\n    margin-bottom: 16px;\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    font-size: 12px;\n    font-weight: 400;\n    line-height: 14px; /* 116.667% */\n}\n.withdraw-next__history": 1, "padding: 5px 0px 7px;\n    border-radius: 6px;\n    background: rgba(47, 47, 47, 0.7);\n    font-size: 14px;\n    font-weight: 400;\n    line-height: 24px; /* 171.429% */\n}\n.withdraw-next__history-title": 1, "margin-bottom: 2px;\n    color: #fff;\n    text-align: center;\n    font-size: 20px;\n    font-weight: 400;\n    line-height: 24px; /* 120% */\n}\n.withdraw-next__history-header": 1, "display: flex;\n    align-items: center;\n    justify-content: space-around;\n}\n.withdraw-next__history-column": 1, "width: 100px;\n    text-align: center;\n}\n.withdraw-next__history-row": 1, "display: flex;\n    align-items: center;\n    justify-content: space-around;\n}\n.withdraw-next__history-value": 1, "text-align: center;\n    width: 100px;\n}\n.withdraw-next__history-value--success": 1, "color: #38b344;\n}\n.withdraw-next__history-value--declined": 1, "color: #b33838;\n}\n\n/**\n  |============================\n  | FAQ\n  |============================\n*/\n.faq": 1, "}\n.faq__container": 1, "padding-top: 18px;\n}\n.faq__title": 1, "margin-bottom: 12px;\n    color: #fff;\n    text-align: center;\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 14px; /* 77.778% */\n}\n.faq__list": 1, "padding: 10px;\n    border-radius: 10px;\n    background: rgba(32, 32, 32, 0.6);\n}\n.faq__item + .faq__item": 1, "margin-top: 12px;\n}\n.faq__question": 1, "margin-bottom: 4px;\n    color: #fff;\n    text-align: justify;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.551px; /* 109.691% */\n}\n.faq__answer": 1, "color: rgba(255, 255, 255, 0.8);\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.551px;\n}\n.faq__support-button": 1, "width: 260px;\n    height: 34px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 6px;\n    padding: 4px 15px;\n    margin: 0 auto;\n    margin-top: 15px;\n    border-radius: 10px;\n    background: var(--accent-color);\n\n    color: #fff;\n    text-align: justify;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.551px; /* 109.691% */\n}\n.faq__support-icon": 1, "width: 20px;\n}\n\n/**\n  |============================\n  | support\n  |============================\n*/\n\n.support": 1, "}\n.support__container": 1, "padding-top: 18px;\n}\n\n.support__title": 1, "margin-bottom: 17px;\n    color: #fff;\n    text-align: center;\n    font-size: 30px;\n\n    font-weight: 500;\n    line-height: 22px; /* 73.333% */\n}\n.support__description": 1, "margin-bottom: 9px;\n    color: rgba(255, 255, 255, 0.9);\n    text-align: center;\n    font-size: 16px;\n\n    font-weight: 400;\n    line-height: 22px; /* 137.5% */\n}\n.support__form": 1, "margin-bottom: 7px;\n    padding: 10px;\n    border-radius: 10px;\n    background: rgba(32, 32, 32, 0.7);\n}\n.support__form-label": 1, "margin-bottom: 8px;\n    display: flex;\n    flex-direction: column;\n    gap: 5px;\n\n    color: #fff;\n    text-align: center;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.913px; /* 127.953% */\n}\n.support__form-input": 1, "padding-left: 8px;\n    height: 33px;\n    border-radius: 8px;\n    background: #3d4042;\n    border: none;\n    outline: none;\n}\n.support__form-label:nth-child(2) > .support__form-input": 1, "height: 100px;\n    resize: none;\n    padding: 8px;\n}\n.support__form-button": 1, "border-radius: 8px;\n    background: var(--accent-color);\n    width: 100%;\n    height: 34px;\n    color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px; /* 111.959% */\n}\n.support__requests": 1, "}\n.support__requests-title": 1, "margin-bottom: 6px;\n    color: #fff;\n    text-align: center;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 16px; /* 100% */\n}\n.support__requests-header": 1, "display: flex;\n    align-items: center;\n    gap: 4px;\n    margin-bottom: 6px;\n}\n.support__requests-subject": 1, "display: flex;\n    align-items: center;\n    padding-left: 5px;\n\n    width: 80%;\n    height: 25px;\n    border-radius: 5px;\n    background: #202020;\n}\n.support__requests-status": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 20%;\n    height: 25px;\n    border-radius: 5px;\n    background: #202020;\n}\n.support__request": 1, "display: flex;\n    align-items: center;\n    gap: 4px;\n    padding-left: 5px;\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 16px; /* 123.077% */\n}\n\n.support__request + .support__request": 1, "padding-top: 4px;\n    margin-top: 4px;\n    border-top: 1px solid rgba(255, 255, 255, 0.3);\n}\n.support__request-text": 1, "width: 80%;\n}\n.support__request-status": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 20%;\n    font-weight: 500;\n}\n.support__request--closed": 1, "opacity: 0.6;\n}\n\n/**\n  |============================\n  | transaction history\n  |============================\n*/\n\n.transaction-history": 1, "}\n.transaction-history__container": 1, "padding-top: 18px;\n}\n\n.transaction-history__title": 1, "margin-bottom: 18px;\n    color: #fff;\n    text-align: center;\n    font-size: 30px;\n    font-weight: 500;\n    line-height: 24px;\n}\n.transaction-history__table": 1, "color: #fff;\n    text-align: center;\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 16px; /* 123.077% */\n}\n@media screen and (min-width: 375px)": 1, ".transaction-history__table": 1, "font-size: 15px;\n    }\n}\n.transaction-history__table-header": 1, "display: flex;\n    align-items: center;\n    gap: 4px;\n    margin-bottom: 11px;\n}\n.transaction-history__table-header > p": 1, "text-align: center;\n    /* width: calc((100% - 8px) / 2); */\n    border-radius: 5px;\n    background: rgba(32, 32, 32, 0.7);\n    padding: 5px 0px 4px 0px;\n}\n.transaction-history__header-amount": 1, "width: 25%;\n}\n.transaction-history__header-type": 1, "width: 50%;\n}\n.transaction-history__header-date": 1, "width: 25%;\n}\n.transaction-history__row": 1, "display: flex;\n    align-items: center;\n    gap: 4px;\n    text-align: center;\n}\n.transaction-history__row + .transaction-history__row": 1, "margin-top: 10px;\n}\n.transaction-history__row-amount": 1, "color: #3f9c51;\n    width: 25%;\n}\n.transaction-history__row-type": 1, "text-align: start;\n    width: 45%;\n}\n.transaction-history__row-date": 1, "width: 25%;\n}\n\n.modal": 1, "position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, 0.5);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n.hidden": 1, "display: none;\n}\n.modal-content": 1, "display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 13px;\n    background-color: white;\n    padding: 20px;\n    border-radius: 10px;\n    text-align: center;\n    width: 300px;\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n.modal-content button": 1, "font-size: 18px;\n    font-weight: 600;\n}\n\n\n.modal-content button": 1, "font-size: 18px;\n    font-weight: 600;\n}\n\n.language__box": 1, "position: relative;\n}\n\n.language__list": 1, "display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n    opacity: 0;\n    visibility: hidden;\n    transition: visibility 0.3s, opacity 0.3s ease;\n    position: absolute;\n    z-index: 10;\n    top: 39px;\n    left: -48px;\n    max-width: 120px;\n    width: 103px;\n    border: 1px solid #333;\n    border-radius: 0px 0px 9px 0px;\n    background: #202020;\n    list-style: none;\n    padding: 4px 0;\n    font-size: 15px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 17.913px;\n}\n\n.language__list.active": 1, "visibility: visible;\n    opacity: 1;\n    /* transition: visibility 0.3s, opacity 0.3s ease; */\n}\n.language__item": 1, "width: calc((100% - 10px) / 2);\n\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n}\n.language__item > img,\n.lang__img": 1, "width: 28px;\n    margin-bottom: 2.5px;\n    cursor: pointer;\n}\n\n\n.chat__container": 1, "padding-top: 11px;\n}\n.chat__title": 1, "margin-bottom: 11px;\n    color: rgba(255, 255, 255, 0.9);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 26px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 22px; /* 84.615% */\n}\n.chat__btn-box": 1, "display: flex;\n    align-items: center;\n    justify-content: space-around;\n    margin-bottom: 14px;\n}\n.chat__btn-link": 1, "width: 110px;\n    height: 37px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: rgba(255, 255, 255, 0.9);\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 15px;\n    font-weight: 400;\n    line-height: 16px; /* 106.667% */\n    border-radius: 15px;\n    background: rgba(57, 57, 57, 0.5);\n}\n.chat__btn-link--red": 1, "background: rgba(106, 49, 49, 0.5);\n}\n.chat__line": 1, "margin: 0 auto;\n    margin-bottom: 14px;\n    width: 90%;\n    height: 1px;\n    background: rgba(255, 255, 255, 0.3);\n}\n.chat__messages": 1, "display: flex;\n    flex-direction: column;\n    padding: 0 10px;\n    height: 300px;\n    overflow: auto;\n}\n.chat__message": 1, "margin-bottom: 16px;\n    width: fit-content;\n    max-width: 90%;\n}\n.chat__message--support": 1, "}\n.chat__message-author": 1, "margin-bottom: 5px;\n    margin-left: 14px;\n    color: rgba(255, 255, 255, 0.9);\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 15px;\n    font-weight: 500;\n    line-height: 16px; /* 106.667% */\n}\n.chat__message-text": 1, "padding: 9px 15px;\n    color: rgba(255, 255, 255, 0.8);\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 18px; /* 112.5% */\n    border-radius: 15px;\n    background: rgba(42, 163, 255, 0.3);\n}\n.chat__message--user": 1, "margin-left: auto;\n}\n.chat__message--user > .chat__message-author": 1, "margin-right: 14px;\n    text-align: end;\n}\n.chat__message--user > .chat__message-text": 1, "border-radius: 15px;\n    background: rgba(128, 128, 128, 0.5);\n}\n.chat__status": 1, "color: rgba(255, 255, 255, 0.5);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 15px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 16px; /* 106.667% */\n    margin-top: 17px;\n    margin-bottom: 4px;\n}\n.chat__input-box": 1, "padding: 0 10px;\n    margin-top: 12px;\n    position: fixed;\n    z-index: 9;\n    bottom: 100px;\n    left: 0;\n    right: 0;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: 500px;\n    width: 100%;\n}\n.chat__input": 1, "padding-left: 15px;\n    width: 100%;\n    height: 46px;\n    border: none;\n    border-radius: 25px;\n    background: rgba(47, 47, 47, 0.5);\n    color: rgba(255, 255, 255, 0.5);\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 16px; /* 88.889% */\n}\n.chat__send-btn": 1, "position: absolute;\n    top: 6px;\n    right: 15px;\n    width: 56px;\n    height: 34px;\n    border-radius: 20px;\n    \n    color: rgba(0, 0, 0, 0.9);\n    text-align: center;\n    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 17.913px; /* 111.959% */\n\tbackground: var(--accent-color);\n}\n\n\n/**\n  |============================\n  | points withdraw\n  |============================\n*/\n.transaction-history__row-type,\n.transaction-history__row-date": 1, "opacity: 0.9;\n}\n\n.withdraw__description-highlight": 1, "color: #fff;\n    font-weight: 500;\n}\n.withdraw__description-text": 1, "margin-top: 12px;\n    color: rgba(255, 255, 255, 0.8);\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 18px;\n}\n.withdraw__description": 1, "padding: 9px 10px;\n    margin-bottom: 12px;\n\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 18px; /* 112.5% */\n\n    border-radius: 10px;\n    background: #161919;\n}\n\n.withdraw__points-balance": 1, "padding-top: 9px;\n    display: flex;\n    flex-direction: column;\n    gap: 6px;\n    margin-bottom: 12px;\n    border-radius: 10px;\n    background: #161919;\n\n    color: #fff;\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 18px; /* 112.5% */\n}\n.withdraw__points-amount": 1, "color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 18px; /* 112.5% */\n}\n.withdraw__points-link": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 36px;\n    border-radius: 0px 0px 10px 10px;\n    background: #ffa800;\n\n    color: rgba(0, 0, 0, 0.8);\n    text-align: center;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 600;\n    line-height: 14px; /* 87.5% */\n}\n\n.withdraw__info": 1, "padding: 10px;\n    border-radius: 10px;\n    background: #161919;\n}\n.withdraw__info-container": 1, "}\n.withdraw__info-title": 1, "margin-bottom: 18px;\n    color: #fff;\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 18px; /* 100% */\n}\n.withdraw__info-subtitle": 1, "margin-bottom: 11px;\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 18px; /* 112.5% */\n}\n.withdraw__info-list": 1, "margin-bottom: 12px;\n    list-style: none;\n    display: flex;\n    align-items: center;\n    gap: 5px;\n    flex-wrap: wrap;\n}\n\n.withdraw__info-item": 1, "display: flex;\n    width: calc((100% - 5px) / 2);\n\n    padding: 8px 8px 8px 15px;\n\n    border-radius: 10px 3px 3px 10px;\n    background: rgba(255, 255, 255, 0.1);\n}\n.withdraw__info-item:nth-child(2n)": 1, "padding: 8px 15px 8px 8px;\n\n    justify-content: end;\n    border-radius: 3px 10px 10px 3px;\n}\n.withdraw__info-text": 1, "width: auto;\n    color: rgba(255, 255, 255, 0.8);\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 18px; /* 112.5% */\n\theight:60px;\n}\n.withdraw__info-text--right": 1, "width: auto;\n    text-align: end;\n}\n.withdraw__info-percentage": 1, "color: #549b5c;\n}\n\n.withdraw__points-subtitle": 1, "margin-bottom: 12px;\n    color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 18px; /* 112.5% */\n}\n.withdraw__points-table-link": 1, "opacity: 0.9;\n    color: #55a6e5;\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 16px;\n}\n\n.referral-contest__time-label": 1, "color: rgba(255, 255, 255, 0.9);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 14px;\n    font-weight: 400;\n    line-height: 18px; /* 128.571% */\n    opacity: 0.9;\n    margin-bottom: 6px;\n}\n.referral-contest__time": 1, "margin-bottom: 14px;\n    color: rgba(255, 255, 255, 0.9);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 18px; /* 100% */\n}\n.referral-contest__status": 1, "margin-bottom: 12px;\n    padding-top: 9px;\n    border-radius: 10px;\n    background: rgba(22, 25, 25, 0.8);\n}\n.referral-contest__stats": 1, "display: flex;\n    align-items: center;\n    justify-content: space-around;\n    margin-bottom: 9px;\n}\n.referral-contest__referrals,\n.referral-contest__reward": 1, "color: #fff;\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-weight: 500;\n    line-height: 18px; /* 112.5% */\n}\n.referral-contest__referral-count": 1, "color: rgba(255, 255, 255, 0.8);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 18px; /* 112.5% */\n}\n.referral-contest__reward-amount": 1, "margin-top: 3px;\n    color: #3f9c51;\n    text-align: center;\n    font-size: 15px;\n    font-weight: 500;\n    line-height: 16px; /* 106.667% */\n}\n.referral-contest__invite-button": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 36px;\n    border-radius: 0px 0px 10px 10px;\n    background: #ffa800;\n\n    color: rgba(0, 0, 0, 0.8);\n    text-align: center;\n    font-size: 16px;\n    font-style: normal;\n    font-weight: 600;\n    line-height: 14px; /* 87.5% */\n}\n.referral-contest__leaderboard-title": 1, "margin-bottom: 13px;\n    color: rgba(255, 255, 255, 0.85);\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 22px;\n    font-weight: 400;\n    line-height: 18px; /* 81.818% */\n}\n.referral-contest__leaderboard": 1, "color: #fff;\n    text-align: center;\n    font-size: 14px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 16px; /* 123.077% */\n}\n@media screen and (min-width: 375px)": 1, ".referral-contest__leaderboard": 1, "font-size: 16px;\n    }\n}\n.referral-contest__leaderboard-header": 1, "display: flex;\n    align-items: center;\n    gap: 2px;\n    margin-bottom: 11px;\n}\n.referral-contest__leaderboard-header > p": 1, "text-align: center;\n    /* width: calc((100% - 8px) / 2); */\n    border-radius: 5px;\n    background: rgba(32, 32, 32, 0.7);\n    padding: 5px 0px 4px 0px;\n}\n.referral-contest__leaderboard-rank": 1, "width: 8%;\n}\n.referral-contest__leaderboard-nickname": 1, "width: 42%;\n}\n.referral-contest__leaderboard-refs": 1, "width: 25%;\n}\n.referral-contest__leaderboard-reward": 1, "width: 25%;\n}\n.referral-contest__leaderboard-entry": 1, "display: flex;\n    align-items: center;\n    gap: 2px;\n    text-align: center;\n\n    color: #fff;\n    font-size: 13px;\n    font-style: normal;\n    font-weight: 400;\n    line-height: 16px; /* 123.077% */\n    opacity: 0.9;\n}\n.referral-contest__leaderboard-entry + .referral-contest__leaderboard-entry": 1, "margin-top: 18px;\n}\n.referral-contest__leaderboard-img": 1, "display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 8%;\n    position: relative;\n}\n.referral-contest__leaderboard-img > img": 1, "position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    z-index: -1;\n}\n.referral-contest__leaderboard-name": 1, "width: 42%;\n}\n.referral-contest__leaderboard-referrals": 1, "width: 25%;\n}\n.referral-contest__leaderboard-prize": 1, "color: #3f9c51;\n    width: 25%;\n}\n\n\n\n\n\n\n\n\n\n\n/* frozen */\n.power__link--frozen": 1, "margin-top: 9px;\n    color: #fff;\n    justify-content: start;\n    padding: 8px 24px;\n    border-radius: 9px;\n    background: #2cadef;\n\t\n\tdisplay: flex; /* Ð£Ð±ÐµÐ´Ð¸Ð¼ÑÑ, ÑÑÐ¾ ÑÐ»ÐµÐ¼ÐµÐ½Ñ ÑÐ²Ð»ÑÐµÑÑÑ flex-ÐºÐ¾Ð½ÑÐµÐ¹Ð½ÐµÑÐ¾Ð¼ */\n    justify-content: center; /* ÐÑÑÐ°Ð²Ð½Ð¸Ð²Ð°Ð½Ð¸Ðµ ÑÐ¾Ð´ÐµÑÐ¶Ð¸Ð¼Ð¾Ð³Ð¾ Ð¿Ð¾ ÑÐµÐ½ÑÑÑ Ð³Ð¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»ÑÐ½Ð¾ */\n    align-items: center; \n\t\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);\n}\n\n.energy__container": 1, "padding-top: 19px;\n}\n.container": 1, "}\n.energy__title": 1, "margin: 0;\n    margin-bottom: 14px;\n    color: #fff;\n    text-align: center;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 30px;\n    font-weight: 500;\n    line-height: 17.551px; /* 58.502% */\n}\n.energy__link": 1, "color: #2aa3ff;\n    text-align: center;\n    margin: 0 auto;\n    margin-bottom: 12px;\n    display: block;\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n    font-size: 16px;\n    font-weight: 500;\n    line-height: 17.913px; /* 111.959% */\n}\n.energy__stats": 1, "display: flex;\n    flex-direction: column;\n    gap: 8px;\n    margin-bottom: 8px;\n}\n.energy__stat": 1, "display: flex;\n    flex-direction: column;\n    padding: 8px 11px;\n    border-radius: 11px;\n    border: 1px solid #000;\n    background: rgba(47, 47, 47, 0.7);\n    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);\n    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);\n\n    font-style: normal;\n    font-weight: 500;\n    line-height: 137.5%;\n}\n.energy__label": 1, "color: rgba(255, 255, 255, 0.7);\n    font-size: 13px;\n}\n.energy__value": 1, "color: rgba(255, 255, 255, 0.4);\n    font-size: 37px;\n    line-height: 85%;\n}\n.energy__stat--claim": 1, "}\n.energy__button": 1, "margin-top: 8px;\n    height: 34px;\n    border-radius: 9px;\n    background: #ff9f00;\n    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);\n\n    color: #261800;\n    text-align: center;\n    font-size: 18px;\n    font-style: normal;\n    font-weight: 500;\n    line-height: 99.519%;\n}\n.energy__value--white": 1, "color: #fff;\n}\n.energy__value--green": 1, "color: #3f9c51;\n}\n.energy__note": 1}, "properties": {"--main-color": 1, "--secondary-color": 1, "--accent-color": 1, "--warning-color": 1, "--text-shadow": 1, "padding": 65, "margin": 21, "box-sizing": 1, "background-color": 7, "border": 15, "cursor": 6, "display": 104, "text-decoration": 1, "color": 130, "min-height": 8, "height": 53, "background-image": 8, "background-repeat": 6, "background-position": 7, "background-size": 6, "overflow": 4, "background": 82, "font-family": 7, "-moz-appearance": 1, "-webkit-appearance": 2, "appearance": 1, "width": 98, "max-width": 16, "position": 39, "flex": 1, "overflow-y": 1, "max-height": 1, "flex-direction": 22, "opacity": 22, "visibility": 8, "transition": 11, "z-index": 17, "top": 21, "left": 19, "border-radius": 75, "list-style": 5, "font-size": 154, "font-style": 94, "font-weight": 149, "line-height": 125, "border-top": 4, "li": 1, "inset": 1, "transform": 14, "text-align": 95, "justify-content": 39, "align-items": 68, "backdrop-filter": 9, "-webkit-backdrop-filter": 7, "text-shadow": 51, "margin-top": 30, "flex-shrink": 1, "box-shadow": 19, "margin-bottom": 89, "animation": 2, "right": 8, "gap": 41, "margin-left": 16, "padding-bottom": 8, "min-width": 6, "main-blur": 1, "content": 2, "span": 3, "animation-delay": 3, "padding-top": 18, "text-transform": 1, "margin-right": 9, "bottom": 4, "-webkit-box-align": 2, "-ms-flex-align": 2, "progress--line": 1, "input": 1, "padding-left": 7, "flex-wrap": 4, "-ms-flex-preferred-size": 1, "flex-basis": 2, "method": 4, "-webkit-box-pack": 1, "-ms-flex-pack": 1, "outline": 3, "padding-right": 1, "info-text": 1, "text": 1, "column-gap": 1, "row-gap": 1, "form-label": 1, "resize": 1, "info-item": 1}, "colors": {"#000": 7, "#fff": 64, "#2cadef": 6, "#d11b1b": 1, "rgba(0, 0, 0, 0.25)": 58, "#000000": 1, "#800000": 1, "#f5f5f5": 1, "#333": 3, "#202020": 5, "rgba(47, 47, 47, 0.7)": 12, "rgba(47, 47, 47, 0.8)": 3, "#353535": 1, "#131313": 1, "rgba(0, 0, 0, 0.15)": 4, "rgba(255, 255, 255, 0.9)": 9, "rgba(255, 255, 255, 0.6)": 2, "rgba(47, 47, 47, 0.4)": 1, "#aaa": 1, "rgba(255, 255, 255, 0.7)": 9, "#4ec3ff": 3, "rgba(47, 47, 47, 0.5)": 3, "rgba(121, 222, 131, 0.7)": 1, "#2CADEF": 1, "#A0DFFF": 1, "#393939": 1, "rgba(255, 255, 255, 0.5)": 3, "rgba(255, 255, 255, 0.8)": 19, "#549b5c": 3, "#2aa3ff": 4, "rgba(255, 255, 255, 0.16)": 4, "rgba(128, 128, 128, 0.3)": 2, "#579d5c": 1, "rgba(47, 47, 47, 0.6)": 1, "#54819b": 1, "rgba(0, 0, 0, 0.1)": 1, "#9e4636": 1, "rgba(128, 128, 128, 0.5)": 2, "#38b344": 1, "#b33838": 1, "rgba(32, 32, 32, 0.6)": 1, "rgba(32, 32, 32, 0.7)": 3, "#3d4042": 1, "rgba(255, 255, 255, 0.3)": 2, "#3f9c51": 4, "rgba(0, 0, 0, 0.5)": 1, "rgba(0, 0, 0, 0.2)": 1, "rgba(57, 57, 57, 0.5)": 1, "rgba(106, 49, 49, 0.5)": 1, "rgba(42, 163, 255, 0.3)": 1, "rgba(0, 0, 0, 0.9)": 1, "#161919": 3, "#ffa800": 2, "rgba(0, 0, 0, 0.8)": 2, "rgba(255, 255, 255, 0.1)": 1, "#55a6e5": 1, "rgba(22, 25, 25, 0.8)": 1, "rgba(255, 255, 255, 0.85)": 1, "rgba(255, 255, 255, 0.4)": 1, "#ff9f00": 1, "#261800": 1}, "fonts": ["\"Unbounded\"", "\"Lekton\", monospace", "\"Hind\"", "\"<PERSON><PERSON><PERSON>\"", "\"<PERSON>ubik\", sans-serif"], "media_queries": [{"file": "styles.css", "query": "screen and (min-width: 375px)"}, {"file": "styles.css", "query": "screen and (min-width: 425px)"}, {"file": "styles.css", "query": "screen and (min-width: 375px)"}, {"file": "styles.css", "query": "screen and (min-width: 425px)"}, {"file": "styles.css", "query": "screen and (min-width: 375px)"}, {"file": "styles.css", "query": "screen and (min-width: 375px)"}]}, "js_analysis": {"total_files": 1, "total_size": 4358, "files": [{"file": "modal-info.js", "size": 4358, "lines": 136, "functions_count": 1, "jquery_usage": false}], "functions": [{"file": "modal-info.js", "name": "showModal"}], "variables": {"modalBackdrop": 1, "infoButton": 1, "menuButton": 1, "burgerLayout": 1, "exchangeIcon": 1, "exchangeButton": 1, "layout": 1, "cancelButton": 1, "languageButton": 1, "languageLayout": 1, "openModalButton": 1, "modalOverlay": 1, "closeModal": 1, "modal": 1, "modalMessage": 1, "modalClose": 1, "modalSuccess": 1, "modalError": 1, "modalContent": 1}, "jquery_usage": false, "ajax_calls": [], "event_listeners": [{"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "animationend"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}, {"file": "modal-info.js", "event": "click"}]}, "image_analysis": {"total_files": 29, "total_size": 475535, "files": [{"file": "arrow-left.png", "size": 343, "type": ".png", "size_category": "small"}, {"file": "arrow-rigth.png", "size": 351, "type": ".png", "size_category": "small"}, {"file": "bg-tasks.png", "size": 60771, "type": ".png", "size_category": "large"}, {"file": "bg.png", "size": 46061, "type": ".png", "size_category": "medium"}, {"file": "burger-menu.png", "size": 604, "type": ".png", "size_category": "small"}, {"file": "DE.png", "size": 209, "type": ".png", "size_category": "small"}, {"file": "earn.png", "size": 1477, "type": ".png", "size_category": "small"}, {"file": "EN.png", "size": 4676, "type": ".png", "size_category": "small"}, {"file": "ES.png", "size": 2399, "type": ".png", "size_category": "small"}, {"file": "exchange-icon.png", "size": 1486, "type": ".png", "size_category": "small"}, {"file": "FR.png", "size": 2512, "type": ".png", "size_category": "small"}, {"file": "gift.png", "size": 1674, "type": ".png", "size_category": "small"}, {"file": "hero-bg-two.png", "size": 243554, "type": ".png", "size_category": "large"}, {"file": "hero-bg.png", "size": 52322, "type": ".png", "size_category": "large"}, {"file": "ID.png", "size": 271, "type": ".png", "size_category": "small"}, {"file": "info.png", "size": 2848, "type": ".png", "size_category": "small"}, {"file": "IT.png", "size": 1543, "type": ".png", "size_category": "small"}, {"file": "miner.png", "size": 3935, "type": ".png", "size_category": "small"}, {"file": "money.png", "size": 9410, "type": ".png", "size_category": "medium"}, {"file": "NL.png", "size": 1525, "type": ".png", "size_category": "small"}, {"file": "PL.png", "size": 1497, "type": ".png", "size_category": "small"}, {"file": "plane.png", "size": 3394, "type": ".png", "size_category": "small"}, {"file": "plus.png", "size": 807, "type": ".png", "size_category": "small"}, {"file": "power.png", "size": 1389, "type": ".png", "size_category": "small"}, {"file": "red-bg.png", "size": 23775, "type": ".png", "size_category": "medium"}, {"file": "RU.png", "size": 1513, "type": ".png", "size_category": "small"}, {"file": "tasks.png", "size": 1471, "type": ".png", "size_category": "small"}, {"file": "TR.png", "size": 2599, "type": ".png", "size_category": "small"}, {"file": "withdraw.png", "size": 1119, "type": ".png", "size_category": "small"}], "types": {".png": 29}, "size_distribution": {"small": 23, "medium": 3, "large": 3}}, "structure_analysis": {"directories": {"root": {"files": 2, "subdirs": 7, "size": 11134}, "assets": {"files": 0, "subdirs": 0, "size": 0}, "css": {"files": 1, "subdirs": 0, "size": 69778}, "data": {"files": 0, "subdirs": 0, "size": 0}, "fonts": {"files": 0, "subdirs": 0, "size": 0}, "html": {"files": 1, "subdirs": 0, "size": 29286}, "images": {"files": 29, "subdirs": 0, "size": 475535}, "js": {"files": 1, "subdirs": 0, "size": 4358}}, "file_types": {".json": 1, ".html": 2, ".css": 1, ".png": 29, ".js": 1}, "total_files": 34, "total_size": 590091}, "security_analysis": {"potential_issues": [], "external_resources": [{"file": "1392053_ca4cea3e6d22bad.html", "tag": "script", "src": "https://code.jquery.com/jquery-3.6.0.min.js"}, {"file": "1392053_ca4cea3e6d22bad.html", "tag": "script", "src": "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"}], "forms_without_csrf": [], "inline_scripts": [{"file": "1392053_ca4cea3e6d22bad.html", "content_length": 526}, {"file": "1392053_ca4cea3e6d22bad.html", "content_length": 8289}, {"file": "1392053_ca4cea3e6d22bad.html", "content_length": 574}, {"file": "1392053_ca4cea3e6d22bad.html", "content_length": 815}], "mixed_content": []}, "performance_analysis": {"total_requests": 32, "total_size": 578957, "largest_files": [{"path": "test_source\\images\\hero-bg-two.png", "name": "hero-bg-two.png", "size": 243554, "type": ".png"}, {"path": "test_source\\css\\styles.css", "name": "styles.css", "size": 69778, "type": ".css"}, {"path": "test_source\\images\\bg-tasks.png", "name": "bg-tasks.png", "size": 60771, "type": ".png"}, {"path": "test_source\\images\\hero-bg.png", "name": "hero-bg.png", "size": 52322, "type": ".png"}, {"path": "test_source\\images\\bg.png", "name": "bg.png", "size": 46061, "type": ".png"}, {"path": "test_source\\html\\1392053_ca4cea3e6d22bad.html", "name": "1392053_ca4cea3e6d22bad.html", "size": 29286, "type": ".html"}, {"path": "test_source\\images\\red-bg.png", "name": "red-bg.png", "size": 23775, "type": ".png"}, {"path": "test_source\\images\\money.png", "name": "money.png", "size": 9410, "type": ".png"}, {"path": "test_source\\images\\EN.png", "name": "EN.png", "size": 4676, "type": ".png"}, {"path": "test_source\\js\\modal-info.js", "name": "modal-info.js", "size": 4358, "type": ".js"}], "optimization_suggestions": ["发现 3 个大图片文件 (>50KB)，建议压缩优化"]}}