# CoreX MINER 爬虫使用指南

## 🎯 项目概述

这是一个专门用于分析 CoreX MINER 加密货币云挖矿网站的Python爬虫项目。该爬虫能够：

- ✅ 自动处理Brotli压缩内容
- ✅ 提取网站的所有链接和图片
- ✅ 识别Telegram推广链接
- ✅ 分析挖矿相关数据
- ✅ 生成详细的分析报告
- ✅ 支持多种数据导出格式

## 📁 项目文件结构

```
├── corex_spider.py      # 主爬虫类（核心功能）
├── run_spider.py        # 运行脚本（命令行工具）
├── config.py           # 配置文件
├── utils.py            # 工具类
├── analyze_results.py  # 结果分析脚本
├── test_brotli.py      # Brotli测试脚本
├── requirements.txt    # 依赖包列表
├── README.md          # 项目说明
├── 使用指南.md         # 中文使用指南
├── data/              # 数据输出目录
└── logs/              # 日志文件目录
```

## 🚀 快速开始

### 1. 环境准备
确保已安装Python 3.8+，然后安装依赖：
```bash
py -m pip install -r requirements.txt
```

### 2. 基本使用
```bash
# 运行爬虫（默认设置）
py run_spider.py

# 详细输出模式
py run_spider.py -v

# 指定输出文件名
py run_spider.py -o my_analysis

# 只保存JSON格式
py run_spider.py -f json
```

### 3. 查看结果
```bash
# 运行分析脚本
py analyze_results.py
```

## 📊 输出文件说明

### JSON数据文件 (`data/corex_data.json`)
包含完整的爬取数据：
- `title`: 网页标题
- `links`: 所有链接信息
- `images`: 图片资源信息
- `telegram_links`: Telegram链接列表
- `text_content`: 完整文本内容

### Excel文件 (`data/corex_data.xlsx`)
包含多个工作表：
- **基本信息**: 网站概览
- **链接**: 所有链接详情
- **图片**: 图片资源列表
- **Telegram链接**: Telegram相关链接

### 分析报告 (`data/corex_data_report.json`)
包含数据统计和分析结果

## 🔍 分析结果解读

### 网站功能模块
- **FAQ**: 常见问题
- **Support**: 客服支持
- **Referral program**: 推荐计划
- **Transaction history**: 交易历史
- **Withdraw**: 提现功能
- **Tasks**: 任务系统
- **Speed up**: 加速/购买功能

### 关键发现
1. **Telegram Bot**: `core_xbot`
2. **推荐ID**: `1392053`
3. **解锁价格**: $24.93 - $2999.93
4. **挖矿货币**: Hash → USDT
5. **功能完整性**: 具备完整的挖矿平台功能

## ⚙️ 高级配置

### 修改目标URL
编辑 `config.py` 文件中的 `TARGET_URLS`:
```python
TARGET_URLS = {
    'main': '你的目标URL',
    'base': '基础域名'
}
```

### 调整请求参数
在 `config.py` 中修改：
```python
REQUEST_CONFIG = {
    'timeout': 10,           # 超时时间
    'max_retries': 3,        # 重试次数
    'retry_delay': 2,        # 重试延时
    'random_delay_range': (1, 3),  # 随机延时范围
}
```

## 🛠️ 故障排除

### 常见问题

**Q: 爬虫运行失败，显示编码错误**
A: 这通常是因为网站使用了压缩传输。本爬虫已经处理了Brotli、Gzip等压缩格式。

**Q: 获取的数据为空或不完整**
A: 可能原因：
- 网站有反爬虫机制
- 需要登录才能访问
- 网站结构发生变化

**Q: 如何分析其他类似网站？**
A: 修改 `config.py` 中的目标URL，并根据需要调整 `parse_main_page` 方法。

### 调试技巧

1. **使用详细模式**:
   ```bash
   py run_spider.py -v
   ```

2. **检查日志文件**:
   ```bash
   # 查看最新日志
   type logs\corex_spider_*.log
   ```

3. **测试网站响应**:
   ```bash
   py test_brotli.py
   ```

## 📋 技术特性

### 反爬虫对策
- ✅ 随机User-Agent
- ✅ 随机请求延时
- ✅ 自动重试机制
- ✅ 请求头伪装

### 数据处理
- ✅ Brotli/Gzip解压缩
- ✅ 多编码支持
- ✅ HTML解析
- ✅ 链接去重和分类

### 输出格式
- ✅ JSON（结构化数据）
- ✅ Excel（多工作表）
- ✅ 详细日志记录

## ⚖️ 使用须知

### 合规使用
- 🚨 请遵守网站的robots.txt规则
- ⚖️ 遵守相关法律法规
- 🤝 尊重网站服务条款
- 📊 仅用于学习和研究目的

### 道德准则
- 💡 适当控制请求频率
- 🔄 不要对服务器造成过大压力
- 📝 保护用户隐私信息
- 💾 合理使用爬取的数据

## 🔄 更新和维护

### 版本历史
- v1.0.0: 初始版本，支持基本爬取功能
- v1.1.0: 添加Brotli解压缩支持
- v1.2.0: 增强数据分析功能

### 后续计划
- [ ] 支持更多压缩格式
- [ ] 添加代理支持
- [ ] 实现分布式爬取
- [ ] 增加更多分析维度

## 📞 技术支持

如果遇到问题或需要帮助：
1. 查看日志文件获取详细错误信息
2. 检查网络连接和目标网站状态
3. 确认Python环境和依赖包版本
4. 参考故障排除部分的解决方案

---

**注意**: 本工具仅供学习和研究使用，请确保合法合规地使用爬虫技术。
