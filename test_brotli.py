#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Brotli解压缩的脚本
"""

import requests
import brotli
from fake_useragent import UserAgent
from bs4 import BeautifulSoup

def test_brotli_decompression():
    """测试Brotli解压缩"""
    url = "https://app-core-x.online//auth_m/1392053_ca4cea3e6d22bad"
    
    ua = UserAgent()
    headers = {
        'User-Agent': ua.random,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        print(f"正在访问: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"内容编码: {response.headers.get('content-encoding', 'none')}")
        print(f"原始内容长度: {len(response.content)} 字节")
        
        # 检查是否是Brotli压缩
        if response.headers.get('content-encoding') == 'br':
            print("检测到Brotli压缩，正在解压缩...")
            
            # 手动解压缩
            try:
                decompressed = brotli.decompress(response.content)
                html_content = decompressed.decode('utf-8')
                print(f"解压缩成功！解压后长度: {len(html_content)} 字符")
                
                # 保存解压后的HTML
                with open('decompressed.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("解压后的HTML已保存到 decompressed.html")
                
                # 解析HTML
                soup = BeautifulSoup(html_content, 'lxml')
                
                # 提取基本信息
                title = soup.find('title')
                print(f"网页标题: {title.get_text().strip() if title else 'N/A'}")
                
                # 提取链接
                links = soup.find_all('a', href=True)
                print(f"找到链接: {len(links)} 个")
                for i, link in enumerate(links[:5]):  # 只显示前5个
                    print(f"  {i+1}. {link.get('href')} - {link.get_text().strip()}")
                
                # 提取图片
                images = soup.find_all('img')
                print(f"找到图片: {len(images)} 个")
                for i, img in enumerate(images[:3]):  # 只显示前3个
                    print(f"  {i+1}. {img.get('src', '')} - {img.get('alt', '')}")
                
                # 查找挖矿相关内容
                text_content = soup.get_text()
                mining_keywords = ['CALCUMINER', 'BLOCK BREACKER', 'QUANTUM CORE', 'START MINING', 'mining', 'miner']
                found_keywords = []
                for keyword in mining_keywords:
                    if keyword.lower() in text_content.lower():
                        found_keywords.append(keyword)
                
                print(f"找到挖矿关键词: {', '.join(found_keywords)}")
                
                # 查找Telegram链接
                telegram_links = [link.get('href') for link in links if 't.me' in link.get('href', '')]
                print(f"找到Telegram链接: {len(telegram_links)} 个")
                for tg_link in telegram_links:
                    print(f"  - {tg_link}")
                
                return html_content
                
            except Exception as e:
                print(f"Brotli解压缩失败: {e}")
                return None
        else:
            print("未检测到Brotli压缩，使用原始内容")
            return response.text
            
    except Exception as e:
        print(f"请求失败: {e}")
        return None

if __name__ == "__main__":
    result = test_brotli_decompression()
    if result:
        print("\n测试成功！")
    else:
        print("\n测试失败！")
