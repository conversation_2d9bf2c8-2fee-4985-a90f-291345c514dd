#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoreX MINER 全站爬虫
支持递归爬取整个网站的所有页面
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
from fake_useragent import UserAgent
from loguru import logger
from retrying import retry
import pandas as pd
from urllib.parse import urljoin, urlparse, urlunparse
import os
from datetime import datetime
import brotli
import gzip
from collections import deque, defaultdict
import hashlib
import pickle
from pathlib import Path
import threading
from queue import Queue

class FullSiteSpider:
    def __init__(self, base_url="https://app-core-x.online", max_depth=3, max_pages=100):
        """
        初始化全站爬虫
        
        Args:
            base_url: 基础URL
            max_depth: 最大爬取深度
            max_pages: 最大页面数量限制
        """
        self.base_url = base_url
        self.max_depth = max_depth
        self.max_pages = max_pages
        
        # URL管理
        self.visited_urls = set()
        self.url_queue = deque()
        self.failed_urls = set()
        
        # 数据存储
        self.pages_data = {}
        self.site_structure = defaultdict(list)
        
        # 会话管理
        self.session = requests.Session()
        self.ua = UserAgent()
        
        # 进度跟踪
        self.total_found = 0
        self.total_crawled = 0
        
        self.setup_session()
        self.setup_logger()
        self.setup_directories()
        
    def setup_directories(self):
        """创建必要的目录"""
        directories = ['data/full_site', 'data/full_site/pages', 'data/full_site/assets', 'logs']
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
    def setup_logger(self):
        """设置日志"""
        logger.add("logs/full_site_spider_{time}.log", 
                  rotation="1 day", 
                  retention="7 days",
                  level="INFO")
        
    def setup_session(self):
        """设置请求会话"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
        }
        self.session.headers.update(headers)
        
    def normalize_url(self, url):
        """标准化URL"""
        if not url:
            return None
            
        # 处理相对URL
        if url.startswith('/'):
            url = urljoin(self.base_url, url)
        elif not url.startswith('http'):
            return None
            
        # 解析URL
        parsed = urlparse(url)
        
        # 只处理同域名的URL
        if parsed.netloc and self.base_url not in url:
            return None
            
        # 移除fragment
        clean_url = urlunparse((parsed.scheme, parsed.netloc, parsed.path, 
                               parsed.params, parsed.query, ''))
        
        return clean_url
        
    def is_valid_page_url(self, url):
        """检查是否是有效的页面URL"""
        if not url:
            return False
            
        # 排除文件下载链接
        excluded_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.css', '.js', 
                             '.ico', '.svg', '.woff', '.woff2', '.ttf', '.eot']
        
        for ext in excluded_extensions:
            if url.lower().endswith(ext):
                return False
                
        # 排除外部链接
        if 'http' in url and self.base_url not in url:
            return False
            
        return True
        
    def decompress_content(self, response):
        """解压缩响应内容"""
        content_encoding = response.headers.get('content-encoding', '').lower()
        content = response.content
        
        try:
            if content_encoding == 'br':
                logger.debug("检测到Brotli压缩，尝试解压缩...")
                decompressed = brotli.decompress(content)
                return decompressed.decode('utf-8')
            elif content_encoding == 'gzip':
                logger.debug("检测到Gzip压缩，尝试解压缩...")
                decompressed = gzip.decompress(content)
                return decompressed.decode('utf-8')
            elif content_encoding == 'deflate':
                logger.debug("检测到Deflate压缩，尝试解压缩...")
                import zlib
                decompressed = zlib.decompress(content)
                return decompressed.decode('utf-8')
            else:
                return response.text
        except Exception as e:
            logger.warning(f"解压缩失败: {e}，使用原始内容")
            return response.text

    @retry(stop_max_attempt_number=3, wait_fixed=2000)
    def fetch_page(self, url):
        """获取网页内容"""
        try:
            logger.info(f"正在访问: {url}")
            
            # 随机更换User-Agent
            self.session.headers['User-Agent'] = self.ua.random
            
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            # 解压缩内容
            html_content = self.decompress_content(response)
            
            # 随机延时，避免被反爬虫
            delay = random.uniform(2, 5)  # 增加延时以减少服务器压力
            time.sleep(delay)
            
            return html_content
            
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {str(e)}")
            self.failed_urls.add(url)
            raise
            
    def extract_links(self, html_content, current_url):
        """从HTML中提取所有链接"""
        soup = BeautifulSoup(html_content, 'lxml')
        links = set()
        
        # 提取所有a标签的href
        for link in soup.find_all('a', href=True):
            href = link['href']
            normalized_url = self.normalize_url(href)
            
            if normalized_url and self.is_valid_page_url(normalized_url):
                links.add(normalized_url)
                
        # 提取form的action
        for form in soup.find_all('form', action=True):
            action = form['action']
            normalized_url = self.normalize_url(action)
            
            if normalized_url and self.is_valid_page_url(normalized_url):
                links.add(normalized_url)
                
        return links
        
    def parse_page(self, html_content, url):
        """解析单个页面"""
        soup = BeautifulSoup(html_content, 'lxml')
        
        page_data = {
            'url': url,
            'title': '',
            'meta_description': '',
            'meta_keywords': '',
            'links': [],
            'images': [],
            'forms': [],
            'text_content': '',
            'crawl_time': datetime.now().isoformat(),
            'status': 'success'
        }
        
        # 提取标题
        title = soup.find('title')
        if title:
            page_data['title'] = title.get_text().strip()
            
        # 提取meta信息
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            page_data['meta_description'] = meta_desc.get('content', '')
            
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords:
            page_data['meta_keywords'] = meta_keywords.get('content', '')
            
        # 提取所有链接
        for link in soup.find_all('a', href=True):
            page_data['links'].append({
                'url': link['href'],
                'text': link.get_text().strip(),
                'title': link.get('title', ''),
                'is_telegram': 't.me' in link['href']
            })
            
        # 提取图片
        for img in soup.find_all('img'):
            page_data['images'].append({
                'src': img.get('src', ''),
                'alt': img.get('alt', ''),
                'title': img.get('title', '')
            })
            
        # 提取表单
        for form in soup.find_all('form'):
            page_data['forms'].append({
                'action': form.get('action', ''),
                'method': form.get('method', 'get'),
                'inputs': len(form.find_all('input'))
            })
            
        # 提取文本内容
        page_data['text_content'] = soup.get_text().strip()
        
        return page_data
        
    def save_page_data(self, page_data):
        """保存单个页面数据"""
        url = page_data['url']
        
        # 生成文件名（使用URL的hash）
        url_hash = hashlib.md5(url.encode()).hexdigest()
        filename = f"data/full_site/pages/{url_hash}.json"
        
        # 保存页面数据
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(page_data, f, ensure_ascii=False, indent=2)
            
        logger.debug(f"页面数据已保存: {filename}")
        
    def crawl_page(self, url, depth=0):
        """爬取单个页面"""
        if url in self.visited_urls:
            return
            
        if depth > self.max_depth:
            logger.info(f"达到最大深度限制: {url}")
            return
            
        if len(self.visited_urls) >= self.max_pages:
            logger.info(f"达到最大页面数量限制: {self.max_pages}")
            return
            
        try:
            # 获取页面内容
            html_content = self.fetch_page(url)
            
            # 标记为已访问
            self.visited_urls.add(url)
            self.total_crawled += 1
            
            # 解析页面
            page_data = self.parse_page(html_content, url)
            
            # 保存页面数据
            self.save_page_data(page_data)
            self.pages_data[url] = page_data
            
            # 提取新链接
            new_links = self.extract_links(html_content, url)
            
            # 添加新链接到队列
            for link in new_links:
                if link not in self.visited_urls and link not in [item[0] for item in self.url_queue]:
                    self.url_queue.append((link, depth + 1))
                    self.total_found += 1
                    
            # 更新站点结构
            self.site_structure[depth].append(url)
            
            logger.info(f"页面爬取完成: {url} (深度: {depth}, 发现新链接: {len(new_links)})")
            logger.info(f"进度: {self.total_crawled}/{self.total_found} 页面已爬取")
            
        except Exception as e:
            logger.error(f"爬取页面失败: {url}, 错误: {str(e)}")
            
    def run(self, start_url=None):
        """运行全站爬虫"""
        if not start_url:
            start_url = f"{self.base_url}/auth_m/1392053_ca4cea3e6d22bad"
            
        logger.info(f"开始全站爬取: {start_url}")
        logger.info(f"最大深度: {self.max_depth}, 最大页面数: {self.max_pages}")
        
        # 添加起始URL
        self.url_queue.append((start_url, 0))
        self.total_found = 1
        
        # 开始爬取
        while self.url_queue and len(self.visited_urls) < self.max_pages:
            url, depth = self.url_queue.popleft()
            
            if url not in self.visited_urls:
                self.crawl_page(url, depth)
                
        # 保存汇总数据
        self.save_summary()
        
        logger.info("全站爬取完成！")
        logger.info(f"总共爬取页面: {len(self.visited_urls)}")
        logger.info(f"失败页面: {len(self.failed_urls)}")
        
        return self.pages_data

    def save_summary(self):
        """保存爬取汇总数据"""
        summary = {
            'crawl_info': {
                'start_time': datetime.now().isoformat(),
                'base_url': self.base_url,
                'max_depth': self.max_depth,
                'max_pages': self.max_pages,
                'total_crawled': len(self.visited_urls),
                'total_failed': len(self.failed_urls),
                'total_found': self.total_found
            },
            'site_structure': dict(self.site_structure),
            'visited_urls': list(self.visited_urls),
            'failed_urls': list(self.failed_urls),
            'url_analysis': self.analyze_urls(),
            'content_analysis': self.analyze_content()
        }

        # 保存汇总JSON
        summary_file = f"data/full_site/site_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        # 保存Excel报告
        self.save_excel_report(summary)

        logger.info(f"汇总数据已保存: {summary_file}")

    def analyze_urls(self):
        """分析URL模式"""
        url_patterns = defaultdict(int)
        url_params = defaultdict(int)

        for url in self.visited_urls:
            parsed = urlparse(url)

            # 分析路径模式
            path_parts = [part for part in parsed.path.split('/') if part]
            if path_parts:
                pattern = '/' + '/'.join(['*' if part.isdigit() else part for part in path_parts])
                url_patterns[pattern] += 1

            # 分析查询参数
            if parsed.query:
                params = parsed.query.split('&')
                for param in params:
                    if '=' in param:
                        key = param.split('=')[0]
                        url_params[key] += 1

        return {
            'url_patterns': dict(url_patterns),
            'common_parameters': dict(url_params)
        }

    def analyze_content(self):
        """分析内容统计"""
        total_links = 0
        total_images = 0
        total_forms = 0
        telegram_links = 0

        page_types = defaultdict(int)

        for url, page_data in self.pages_data.items():
            total_links += len(page_data.get('links', []))
            total_images += len(page_data.get('images', []))
            total_forms += len(page_data.get('forms', []))

            # 统计Telegram链接
            for link in page_data.get('links', []):
                if link.get('is_telegram'):
                    telegram_links += 1

            # 分析页面类型
            title = page_data.get('title', '').lower()
            if 'login' in title or 'auth' in title:
                page_types['auth'] += 1
            elif 'account' in title or 'profile' in title:
                page_types['account'] += 1
            elif 'payment' in title or 'withdraw' in title:
                page_types['payment'] += 1
            elif 'faq' in title or 'help' in title:
                page_types['help'] += 1
            else:
                page_types['other'] += 1

        return {
            'total_pages': len(self.pages_data),
            'total_links': total_links,
            'total_images': total_images,
            'total_forms': total_forms,
            'telegram_links': telegram_links,
            'page_types': dict(page_types)
        }

    def save_excel_report(self, summary):
        """保存Excel格式的详细报告"""
        excel_file = f"data/full_site/full_site_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 爬取概览
            overview_df = pd.DataFrame([summary['crawl_info']])
            overview_df.to_excel(writer, sheet_name='爬取概览', index=False)

            # URL列表
            urls_df = pd.DataFrame({
                'URL': list(self.visited_urls),
                '状态': ['成功'] * len(self.visited_urls)
            })
            if self.failed_urls:
                failed_df = pd.DataFrame({
                    'URL': list(self.failed_urls),
                    '状态': ['失败'] * len(self.failed_urls)
                })
                urls_df = pd.concat([urls_df, failed_df], ignore_index=True)
            urls_df.to_excel(writer, sheet_name='URL列表', index=False)

            # 页面详情
            pages_list = []
            for url, page_data in self.pages_data.items():
                pages_list.append({
                    'URL': url,
                    '标题': page_data.get('title', ''),
                    '链接数': len(page_data.get('links', [])),
                    '图片数': len(page_data.get('images', [])),
                    '表单数': len(page_data.get('forms', [])),
                    '爬取时间': page_data.get('crawl_time', '')
                })

            if pages_list:
                pages_df = pd.DataFrame(pages_list)
                pages_df.to_excel(writer, sheet_name='页面详情', index=False)

            # 内容分析
            content_analysis = summary['content_analysis']
            analysis_df = pd.DataFrame([content_analysis])
            analysis_df.to_excel(writer, sheet_name='内容分析', index=False)

        logger.info(f"Excel报告已保存: {excel_file}")

    def load_checkpoint(self, checkpoint_file):
        """加载断点数据"""
        try:
            with open(checkpoint_file, 'rb') as f:
                checkpoint = pickle.load(f)

            self.visited_urls = checkpoint['visited_urls']
            self.url_queue = checkpoint['url_queue']
            self.failed_urls = checkpoint['failed_urls']
            self.pages_data = checkpoint['pages_data']
            self.total_found = checkpoint['total_found']
            self.total_crawled = checkpoint['total_crawled']

            logger.info(f"断点数据已加载: {checkpoint_file}")
            return True
        except Exception as e:
            logger.warning(f"加载断点数据失败: {e}")
            return False

    def save_checkpoint(self):
        """保存断点数据"""
        checkpoint = {
            'visited_urls': self.visited_urls,
            'url_queue': self.url_queue,
            'failed_urls': self.failed_urls,
            'pages_data': self.pages_data,
            'total_found': self.total_found,
            'total_crawled': self.total_crawled,
            'timestamp': datetime.now().isoformat()
        }

        checkpoint_file = f"data/full_site/checkpoint_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        with open(checkpoint_file, 'wb') as f:
            pickle.dump(checkpoint, f)

        logger.info(f"断点数据已保存: {checkpoint_file}")
        return checkpoint_file
