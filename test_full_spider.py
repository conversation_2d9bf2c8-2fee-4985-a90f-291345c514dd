#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全站爬虫的简化脚本
"""

from full_site_spider import FullSiteSpider
import os

def test_full_spider():
    """测试全站爬虫"""
    print("🕷️ 开始测试全站爬虫...")
    
    # 创建爬虫实例（小参数测试）
    spider = FullSiteSpider(
        base_url="https://app-core-x.online",
        max_depth=1,  # 只爬1层
        max_pages=3   # 只爬3个页面
    )
    
    print(f"配置: 最大深度={spider.max_depth}, 最大页面={spider.max_pages}")
    
    try:
        # 运行爬虫
        start_url = "https://app-core-x.online/auth_m/1392053_ca4cea3e6d22bad"
        print(f"起始URL: {start_url}")
        
        pages_data = spider.run(start_url)
        
        # 显示结果
        print("\n✅ 测试完成！")
        print(f"成功爬取: {len(spider.visited_urls)} 个页面")
        print(f"失败页面: {len(spider.failed_urls)} 个")
        print(f"发现链接: {spider.total_found} 个")
        
        # 显示爬取的页面
        print("\n📄 爬取的页面:")
        for i, url in enumerate(spider.visited_urls, 1):
            page_data = spider.pages_data.get(url, {})
            title = page_data.get('title', 'N/A')
            print(f"  {i}. {title} - {url}")
            
        # 检查输出文件
        print("\n📁 输出文件:")
        data_dir = "data/full_site"
        if os.path.exists(data_dir):
            files = os.listdir(data_dir)
            for file in files:
                print(f"  - {file}")
        else:
            print("  未找到输出目录")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_spider()
    if success:
        print("\n🎉 全站爬虫测试成功！")
    else:
        print("\n💥 全站爬虫测试失败！")
