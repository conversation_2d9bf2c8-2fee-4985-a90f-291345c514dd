import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { Button, Popup, List } from 'antd-mobile';
import { 
  UnorderedListOutline, 
  AddOutline 
} from 'antd-mobile-icons';
import { RootState } from '../../store';
import { formatNumber } from '../../utils/formatters';
import './Header.scss';

// 语言选项
const languages = [
  { code: 'EN', name: 'English', flag: '/images/EN.png' },
  { code: 'RU', name: 'Русский', flag: '/images/RU.png' },
  { code: 'ES', name: 'Español', flag: '/images/ES.png' },
  { code: 'IT', name: 'Italiano', flag: '/images/IT.png' },
  { code: 'DE', name: '<PERSON><PERSON><PERSON>', flag: '/images/DE.png' },
  { code: 'FR', name: 'Français', flag: '/images/FR.png' },
  { code: 'NL', name: 'Ned<PERSON>s', flag: '/images/NL.png' },
  { code: 'PL', name: '<PERSON><PERSON>', flag: '/images/PL.png' },
  { code: 'TR', name: 'Türkçe', flag: '/images/TR.png' },
  { code: 'ID', name: 'Bahasa Indonesia', flag: '/images/ID.png' },
];

const Header: React.FC = () => {
  const [menuVisible, setMenuVisible] = useState(false);
  const [languageVisible, setLanguageVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(languages[0]);
  
  const user = useSelector((state: RootState) => state.auth.user);

  // 菜单项
  const menuItems = [
    { label: 'FAQ', href: '/faq' },
    { label: 'Support', href: '/support' },
    { label: 'Referral program', href: '/referrals' },
    { label: 'Transaction history', href: '/history' },
  ];

  // 处理语言切换
  const handleLanguageChange = (language: typeof languages[0]) => {
    setCurrentLanguage(language);
    setLanguageVisible(false);
    // TODO: 实现语言切换逻辑
  };

  // 处理菜单项点击
  const handleMenuItemClick = (_href: string) => {
    setMenuVisible(false);
    // TODO: 实现导航逻辑
  };

  return (
    <header className="header">
      <div className="container">
        <nav className="header__nav">
          {/* 汉堡菜单 */}
          <div className="menu__box">
            <Button
              className="header__menu-button"
              onClick={() => setMenuVisible(true)}
              aria-label="打开菜单"
              title="打开菜单"
            >
              <UnorderedListOutline className="header__menu-icon" />
            </Button>

            {/* 菜单弹窗 */}
            <Popup
              visible={menuVisible}
              onMaskClick={() => setMenuVisible(false)}
              position="left"
              bodyStyle={{ width: '200px' }}
            >
              <List className="burger__list">
                {menuItems.map((item, index) => (
                  <List.Item
                    key={index}
                    onClick={() => handleMenuItemClick(item.href)}
                  >
                    {item.label}
                  </List.Item>
                ))}
              </List>
            </Popup>
          </div>

          {/* 语言选择器 */}
          <div className="language__box">
            <Button
              className="language__button"
              onClick={() => setLanguageVisible(true)}
              aria-label={`选择语言，当前：${currentLanguage.name}`}
              title={`选择语言，当前：${currentLanguage.name}`}
            >
              <img
                className="lang__img"
                src={currentLanguage.flag}
                alt={currentLanguage.name}
              />
            </Button>

            {/* 语言选择弹窗 */}
            <Popup
              visible={languageVisible}
              onMaskClick={() => setLanguageVisible(false)}
              position="top"
              bodyStyle={{ 
                width: '200px',
                maxHeight: '300px',
                overflow: 'auto'
              }}
            >
              <List className="language__list">
                {languages.map((language) => (
                  <List.Item
                    key={language.code}
                    onClick={() => handleLanguageChange(language)}
                    arrow={false}
                  >
                    <div className="language__item">
                      <img src={language.flag} alt={language.name} />
                      <span>{language.code}</span>
                    </div>
                  </List.Item>
                ))}
              </List>
            </Popup>
          </div>

          {/* 用户余额 */}
          <div className="header__balance">
            <img 
              src="/images/money.png" 
              alt="USDT" 
              width="31" 
              height="30" 
            />
            <p className="header__balance-amount">
              {formatNumber(user?.balance || 0, 6)}
            </p>
          </div>

          {/* 充值按钮 */}
          <div className="header__user-plus">
            <Button
              className="header__deposit-button"
              size="small"
              onClick={() => {/* TODO: 实现充值逻辑 */}}
              aria-label="充值"
              title="充值"
            >
              <AddOutline />
            </Button>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header; 