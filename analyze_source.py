#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
源码分析工具
分析下载的网站源码结构和内容
"""

import os
import json
import re
from pathlib import Path
from collections import defaultdict, Counter
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime

class SourceAnalyzer:
    def __init__(self, source_dir):
        """
        初始化源码分析器
        
        Args:
            source_dir: 源码目录路径
        """
        self.source_dir = source_dir
        self.analysis_data = {
            'html_analysis': {},
            'css_analysis': {},
            'js_analysis': {},
            'image_analysis': {},
            'structure_analysis': {},
            'security_analysis': {},
            'performance_analysis': {}
        }
        
    def analyze_html_files(self):
        """分析HTML文件"""
        html_dir = os.path.join(self.source_dir, 'html')
        if not os.path.exists(html_dir):
            return
            
        html_files = [f for f in os.listdir(html_dir) if f.endswith('.html')]
        
        html_stats = {
            'total_files': len(html_files),
            'total_size': 0,
            'pages': [],
            'forms': [],
            'links': [],
            'meta_tags': [],
            'scripts': [],
            'stylesheets': []
        }
        
        for html_file in html_files:
            file_path = os.path.join(html_dir, html_file)
            file_size = os.path.getsize(file_path)
            html_stats['total_size'] += file_size
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            soup = BeautifulSoup(content, 'lxml')
            
            # 页面基本信息
            page_info = {
                'file': html_file,
                'size': file_size,
                'title': soup.find('title').get_text() if soup.find('title') else '',
                'meta_description': '',
                'meta_keywords': '',
                'forms_count': len(soup.find_all('form')),
                'links_count': len(soup.find_all('a')),
                'images_count': len(soup.find_all('img')),
                'scripts_count': len(soup.find_all('script')),
                'stylesheets_count': len(soup.find_all('link', rel='stylesheet'))
            }
            
            # Meta标签
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                page_info['meta_description'] = meta_desc.get('content', '')
                
            meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
            if meta_keywords:
                page_info['meta_keywords'] = meta_keywords.get('content', '')
                
            html_stats['pages'].append(page_info)
            
            # 表单分析
            for form in soup.find_all('form'):
                form_info = {
                    'file': html_file,
                    'action': form.get('action', ''),
                    'method': form.get('method', 'get'),
                    'inputs': len(form.find_all('input')),
                    'has_file_upload': bool(form.find('input', type='file'))
                }
                html_stats['forms'].append(form_info)
                
            # 链接分析
            for link in soup.find_all('a', href=True):
                link_info = {
                    'file': html_file,
                    'href': link['href'],
                    'text': link.get_text().strip(),
                    'is_external': link['href'].startswith('http') and 'app-core-x.online' not in link['href'],
                    'is_telegram': 't.me' in link['href']
                }
                html_stats['links'].append(link_info)
                
            # 脚本分析
            for script in soup.find_all('script'):
                script_info = {
                    'file': html_file,
                    'src': script.get('src', ''),
                    'inline': not bool(script.get('src')),
                    'content_length': len(script.get_text()) if not script.get('src') else 0
                }
                html_stats['scripts'].append(script_info)
                
            # 样式表分析
            for link in soup.find_all('link', rel='stylesheet'):
                css_info = {
                    'file': html_file,
                    'href': link.get('href', ''),
                    'media': link.get('media', 'all')
                }
                html_stats['stylesheets'].append(css_info)
                
        self.analysis_data['html_analysis'] = html_stats
        
    def analyze_css_files(self):
        """分析CSS文件"""
        css_dir = os.path.join(self.source_dir, 'css')
        if not os.path.exists(css_dir):
            return
            
        css_files = [f for f in os.listdir(css_dir) if f.endswith('.css')]
        
        css_stats = {
            'total_files': len(css_files),
            'total_size': 0,
            'files': [],
            'selectors': Counter(),
            'properties': Counter(),
            'colors': Counter(),
            'fonts': set(),
            'media_queries': []
        }
        
        for css_file in css_files:
            file_path = os.path.join(css_dir, css_file)
            file_size = os.path.getsize(file_path)
            css_stats['total_size'] += file_size
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 基本信息
            file_info = {
                'file': css_file,
                'size': file_size,
                'lines': len(content.split('\n')),
                'rules_count': len(re.findall(r'\{[^}]*\}', content))
            }
            css_stats['files'].append(file_info)
            
            # 选择器分析
            selectors = re.findall(r'([^{]+)\s*\{', content)
            for selector in selectors:
                clean_selector = selector.strip()
                if clean_selector:
                    css_stats['selectors'][clean_selector] += 1
                    
            # 属性分析
            properties = re.findall(r'([a-zA-Z-]+)\s*:', content)
            for prop in properties:
                css_stats['properties'][prop.strip()] += 1
                
            # 颜色分析
            colors = re.findall(r'#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)', content)
            for color in colors:
                css_stats['colors'][color] += 1
                
            # 字体分析
            fonts = re.findall(r'font-family\s*:\s*([^;]+)', content)
            for font in fonts:
                css_stats['fonts'].add(font.strip())
                
            # 媒体查询分析
            media_queries = re.findall(r'@media\s+([^{]+)', content)
            for mq in media_queries:
                css_stats['media_queries'].append({
                    'file': css_file,
                    'query': mq.strip()
                })
                
        css_stats['fonts'] = list(css_stats['fonts'])
        self.analysis_data['css_analysis'] = css_stats
        
    def analyze_js_files(self):
        """分析JavaScript文件"""
        js_dir = os.path.join(self.source_dir, 'js')
        if not os.path.exists(js_dir):
            return
            
        js_files = [f for f in os.listdir(js_dir) if f.endswith('.js')]
        
        js_stats = {
            'total_files': len(js_files),
            'total_size': 0,
            'files': [],
            'functions': [],
            'variables': Counter(),
            'jquery_usage': False,
            'ajax_calls': [],
            'event_listeners': []
        }
        
        for js_file in js_files:
            file_path = os.path.join(js_dir, js_file)
            file_size = os.path.getsize(file_path)
            js_stats['total_size'] += file_size
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 基本信息
            file_info = {
                'file': js_file,
                'size': file_size,
                'lines': len(content.split('\n')),
                'functions_count': len(re.findall(r'function\s+\w+', content)),
                'jquery_usage': '$' in content or 'jQuery' in content
            }
            js_stats['files'].append(file_info)
            
            # jQuery使用检测
            if '$' in content or 'jQuery' in content:
                js_stats['jquery_usage'] = True
                
            # 函数分析
            functions = re.findall(r'function\s+(\w+)', content)
            for func in functions:
                js_stats['functions'].append({
                    'file': js_file,
                    'name': func
                })
                
            # 变量分析
            variables = re.findall(r'(?:var|let|const)\s+(\w+)', content)
            for var in variables:
                js_stats['variables'][var] += 1
                
            # AJAX调用分析
            ajax_patterns = [
                r'\$\.ajax\(',
                r'\$\.get\(',
                r'\$\.post\(',
                r'fetch\(',
                r'XMLHttpRequest'
            ]
            
            for pattern in ajax_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    js_stats['ajax_calls'].append({
                        'file': js_file,
                        'type': pattern.replace('\\', '').replace('(', '')
                    })
                    
            # 事件监听器分析
            event_patterns = [
                r'addEventListener\([\'"](\w+)[\'"]',
                r'\.on\([\'"](\w+)[\'"]',
                r'\.click\(',
                r'\.submit\('
            ]
            
            for pattern in event_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    js_stats['event_listeners'].append({
                        'file': js_file,
                        'event': match if isinstance(match, str) else pattern
                    })
                    
        self.analysis_data['js_analysis'] = js_stats
        
    def analyze_images(self):
        """分析图片文件"""
        images_dir = os.path.join(self.source_dir, 'images')
        if not os.path.exists(images_dir):
            return
            
        image_files = [f for f in os.listdir(images_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'))]
        
        image_stats = {
            'total_files': len(image_files),
            'total_size': 0,
            'files': [],
            'types': Counter(),
            'size_distribution': {'small': 0, 'medium': 0, 'large': 0}
        }
        
        for image_file in image_files:
            file_path = os.path.join(images_dir, image_file)
            file_size = os.path.getsize(file_path)
            image_stats['total_size'] += file_size
            
            # 文件类型
            ext = os.path.splitext(image_file)[1].lower()
            image_stats['types'][ext] += 1
            
            # 大小分类
            if file_size < 5000:  # < 5KB
                image_stats['size_distribution']['small'] += 1
            elif file_size < 50000:  # < 50KB
                image_stats['size_distribution']['medium'] += 1
            else:  # >= 50KB
                image_stats['size_distribution']['large'] += 1
                
            file_info = {
                'file': image_file,
                'size': file_size,
                'type': ext,
                'size_category': 'small' if file_size < 5000 else 'medium' if file_size < 50000 else 'large'
            }
            image_stats['files'].append(file_info)
            
        self.analysis_data['image_analysis'] = image_stats

    def analyze_structure(self):
        """分析整体结构"""
        structure_stats = {
            'directories': {},
            'file_types': Counter(),
            'total_files': 0,
            'total_size': 0
        }

        for root, dirs, files in os.walk(self.source_dir):
            rel_path = os.path.relpath(root, self.source_dir)
            if rel_path == '.':
                rel_path = 'root'

            structure_stats['directories'][rel_path] = {
                'files': len(files),
                'subdirs': len(dirs),
                'size': 0
            }

            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)

                structure_stats['total_files'] += 1
                structure_stats['total_size'] += file_size
                structure_stats['directories'][rel_path]['size'] += file_size

                # 文件类型统计
                ext = os.path.splitext(file)[1].lower()
                if ext:
                    structure_stats['file_types'][ext] += 1
                else:
                    structure_stats['file_types']['no_extension'] += 1

        self.analysis_data['structure_analysis'] = structure_stats

    def analyze_security(self):
        """安全性分析"""
        security_issues = {
            'potential_issues': [],
            'external_resources': [],
            'forms_without_csrf': [],
            'inline_scripts': [],
            'mixed_content': []
        }

        # 分析HTML文件中的安全问题
        html_dir = os.path.join(self.source_dir, 'html')
        if os.path.exists(html_dir):
            for html_file in os.listdir(html_dir):
                if not html_file.endswith('.html'):
                    continue

                file_path = os.path.join(html_dir, html_file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                soup = BeautifulSoup(content, 'lxml')

                # 外部资源检测
                for tag in soup.find_all(['script', 'link', 'img'], src=True):
                    src = tag.get('src') or tag.get('href')
                    if src and src.startswith('http') and 'app-core-x.online' not in src:
                        security_issues['external_resources'].append({
                            'file': html_file,
                            'tag': tag.name,
                            'src': src
                        })

                # 内联脚本检测
                for script in soup.find_all('script'):
                    if not script.get('src') and script.get_text().strip():
                        security_issues['inline_scripts'].append({
                            'file': html_file,
                            'content_length': len(script.get_text())
                        })

                # 表单CSRF检测
                for form in soup.find_all('form'):
                    has_csrf = bool(form.find('input', attrs={'name': re.compile(r'csrf|token', re.I)}))
                    if not has_csrf:
                        security_issues['forms_without_csrf'].append({
                            'file': html_file,
                            'action': form.get('action', ''),
                            'method': form.get('method', 'get')
                        })

        self.analysis_data['security_analysis'] = security_issues

    def analyze_performance(self):
        """性能分析"""
        performance_stats = {
            'total_requests': 0,
            'total_size': 0,
            'largest_files': [],
            'optimization_suggestions': []
        }

        all_files = []

        # 收集所有文件信息
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                if file in ['index.html', 'download_report.json']:
                    continue

                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)

                all_files.append({
                    'path': file_path,
                    'name': file,
                    'size': file_size,
                    'type': os.path.splitext(file)[1].lower()
                })

                performance_stats['total_requests'] += 1
                performance_stats['total_size'] += file_size

        # 找出最大的文件
        all_files.sort(key=lambda x: x['size'], reverse=True)
        performance_stats['largest_files'] = all_files[:10]

        # 优化建议
        large_images = [f for f in all_files if f['type'] in ['.png', '.jpg', '.jpeg'] and f['size'] > 50000]
        if large_images:
            performance_stats['optimization_suggestions'].append(
                f"发现 {len(large_images)} 个大图片文件 (>50KB)，建议压缩优化"
            )

        css_files = [f for f in all_files if f['type'] == '.css']
        if len(css_files) > 3:
            performance_stats['optimization_suggestions'].append(
                f"发现 {len(css_files)} 个CSS文件，建议合并减少请求数"
            )

        js_files = [f for f in all_files if f['type'] == '.js']
        if len(js_files) > 3:
            performance_stats['optimization_suggestions'].append(
                f"发现 {len(js_files)} 个JS文件，建议合并减少请求数"
            )

        self.analysis_data['performance_analysis'] = performance_stats

    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def generate_report(self):
        """生成完整的分析报告"""
        print("🔍 开始源码分析...")

        # 执行各项分析
        self.analyze_html_files()
        self.analyze_css_files()
        self.analyze_js_files()
        self.analyze_images()
        self.analyze_structure()
        self.analyze_security()
        self.analyze_performance()

        # 显示分析结果
        self.print_analysis_results()

        # 保存详细报告
        self.save_detailed_report()

    def print_analysis_results(self):
        """打印分析结果"""
        print("\n" + "="*60)
        print("📊 源码分析报告")
        print("="*60)

        # 整体结构
        structure = self.analysis_data['structure_analysis']
        print(f"\n🏗️  整体结构:")
        print(f"   总文件数: {structure['total_files']}")
        print(f"   总大小: {self.format_size(structure['total_size'])}")
        print(f"   目录数: {len(structure['directories'])}")

        print(f"\n📁 目录分布:")
        for dir_name, info in structure['directories'].items():
            print(f"   {dir_name}: {info['files']} 文件, {self.format_size(info['size'])}")

        # HTML分析
        html = self.analysis_data['html_analysis']
        if html:
            print(f"\n📄 HTML分析:")
            print(f"   HTML文件: {html['total_files']} 个")
            print(f"   总大小: {self.format_size(html['total_size'])}")
            print(f"   表单数: {len(html['forms'])}")
            print(f"   链接数: {len(html['links'])}")

        # CSS分析
        css = self.analysis_data['css_analysis']
        if css:
            print(f"\n🎨 CSS分析:")
            print(f"   CSS文件: {css['total_files']} 个")
            print(f"   总大小: {self.format_size(css['total_size'])}")
            print(f"   常用属性: {', '.join([prop for prop, count in css['properties'].most_common(5)])}")

        # JavaScript分析
        js = self.analysis_data['js_analysis']
        if js:
            print(f"\n⚡ JavaScript分析:")
            print(f"   JS文件: {js['total_files']} 个")
            print(f"   总大小: {self.format_size(js['total_size'])}")
            print(f"   jQuery使用: {'是' if js['jquery_usage'] else '否'}")
            print(f"   函数数: {len(js['functions'])}")

        # 图片分析
        images = self.analysis_data['image_analysis']
        if images:
            print(f"\n🖼️  图片分析:")
            print(f"   图片文件: {images['total_files']} 个")
            print(f"   总大小: {self.format_size(images['total_size'])}")
            print(f"   文件类型: {dict(images['types'])}")

        # 安全分析
        security = self.analysis_data['security_analysis']
        print(f"\n🔒 安全分析:")
        print(f"   外部资源: {len(security['external_resources'])} 个")
        print(f"   内联脚本: {len(security['inline_scripts'])} 个")
        print(f"   无CSRF保护表单: {len(security['forms_without_csrf'])} 个")

        # 性能分析
        performance = self.analysis_data['performance_analysis']
        print(f"\n⚡ 性能分析:")
        print(f"   总请求数: {performance['total_requests']}")
        print(f"   总大小: {self.format_size(performance['total_size'])}")

        if performance['optimization_suggestions']:
            print(f"\n💡 优化建议:")
            for suggestion in performance['optimization_suggestions']:
                print(f"   • {suggestion}")

        print("="*60)

    def save_detailed_report(self):
        """保存详细报告"""
        # 保存JSON报告
        report_data = {
            'analysis_time': datetime.now().isoformat(),
            'source_directory': self.source_dir,
            **self.analysis_data
        }

        report_file = os.path.join(self.source_dir, 'source_analysis_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细报告已保存: {report_file}")

def main():
    import sys

    if len(sys.argv) != 2:
        print("使用方法: py analyze_source.py <源码目录>")
        print("示例: py analyze_source.py test_source")
        return

    source_dir = sys.argv[1]

    if not os.path.exists(source_dir):
        print(f"错误: 目录不存在 - {source_dir}")
        return

    analyzer = SourceAnalyzer(source_dir)
    analyzer.generate_report()

if __name__ == "__main__":
    main()
