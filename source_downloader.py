#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网站源码下载器
专门用于下载和保存网站的完整源码，包括HTML、CSS、JS、图片等资源
"""

import requests
from bs4 import BeautifulSoup
import os
import re
import time
import random
from urllib.parse import urljoin, urlparse, unquote
from pathlib import Path
import hashlib
import json
from fake_useragent import UserAgent
from loguru import logger
import brotli
import gzip
from datetime import datetime
import mimetypes

class SourceDownloader:
    def __init__(self, base_url, output_dir="downloaded_site"):
        """
        初始化源码下载器
        
        Args:
            base_url: 网站基础URL
            output_dir: 输出目录
        """
        self.base_url = base_url.rstrip('/')
        self.output_dir = output_dir
        self.session = requests.Session()
        self.ua = UserAgent()
        
        # 下载统计
        self.downloaded_files = {}
        self.failed_downloads = []
        self.total_size = 0
        
        # 文件类型映射
        self.file_extensions = {
            'text/html': '.html',
            'text/css': '.css',
            'application/javascript': '.js',
            'text/javascript': '.js',
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/svg+xml': '.svg',
            'image/x-icon': '.ico',
            'application/json': '.json',
            'font/woff': '.woff',
            'font/woff2': '.woff2',
            'application/font-woff': '.woff',
            'application/font-woff2': '.woff2'
        }
        
        self.setup_session()
        self.setup_directories()
        self.setup_logger()
        
    def setup_session(self):
        """设置请求会话"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
        
    def setup_directories(self):
        """创建必要的目录"""
        directories = [
            self.output_dir,
            f"{self.output_dir}/html",
            f"{self.output_dir}/css", 
            f"{self.output_dir}/js",
            f"{self.output_dir}/images",
            f"{self.output_dir}/fonts",
            f"{self.output_dir}/assets",
            f"{self.output_dir}/data"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
    def setup_logger(self):
        """设置日志"""
        logger.add(f"logs/source_downloader_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log", 
                  rotation="1 day", 
                  retention="7 days",
                  level="INFO")
        
    def decompress_content(self, response):
        """解压缩响应内容"""
        content_encoding = response.headers.get('content-encoding', '').lower()
        
        try:
            if content_encoding == 'br':
                try:
                    decompressed = brotli.decompress(response.content)
                    return decompressed.decode('utf-8')
                except:
                    return response.text
            elif content_encoding == 'gzip':
                decompressed = gzip.decompress(response.content)
                return decompressed.decode('utf-8')
            else:
                return response.text
        except Exception as e:
            logger.warning(f"解压缩失败: {e}，使用原始内容")
            return response.text
            
    def get_file_extension(self, url, content_type=None):
        """根据URL和内容类型确定文件扩展名"""
        # 首先尝试从URL获取扩展名
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        if '.' in path:
            ext = os.path.splitext(path)[1].lower()
            if ext in ['.html', '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2']:
                return ext
                
        # 如果URL没有扩展名，根据内容类型确定
        if content_type:
            return self.file_extensions.get(content_type, '')
            
        return ''
        
    def sanitize_filename(self, filename):
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # 限制文件名长度
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
            
        return filename
        
    def download_file(self, url, save_path=None):
        """下载单个文件"""
        try:
            logger.info(f"下载文件: {url}")
            
            # 随机更换User-Agent
            self.session.headers['User-Agent'] = self.ua.random
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # 获取内容类型
            content_type = response.headers.get('content-type', '').split(';')[0].strip()
            
            # 确定保存路径
            if not save_path:
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path) or 'index'
                
                # 添加扩展名
                ext = self.get_file_extension(url, content_type)
                if not filename.endswith(ext) and ext:
                    filename += ext
                    
                # 根据文件类型确定子目录
                if content_type.startswith('text/html'):
                    subdir = 'html'
                elif content_type.startswith('text/css'):
                    subdir = 'css'
                elif content_type.startswith('application/javascript') or content_type.startswith('text/javascript'):
                    subdir = 'js'
                elif content_type.startswith('image/'):
                    subdir = 'images'
                elif 'font' in content_type:
                    subdir = 'fonts'
                else:
                    subdir = 'assets'
                    
                filename = self.sanitize_filename(filename)
                save_path = os.path.join(self.output_dir, subdir, filename)
                
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 保存文件
            if content_type.startswith('text/') or content_type.startswith('application/json'):
                # 文本文件，解压缩后保存
                content = self.decompress_content(response)
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            else:
                # 二进制文件，直接保存
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                    
            # 记录下载信息
            file_size = len(response.content)
            self.downloaded_files[url] = {
                'local_path': save_path,
                'size': file_size,
                'content_type': content_type,
                'download_time': datetime.now().isoformat()
            }
            self.total_size += file_size
            
            logger.info(f"文件已保存: {save_path} ({file_size} bytes)")
            
            # 随机延时
            time.sleep(random.uniform(0.5, 2))
            
            return save_path
            
        except Exception as e:
            logger.error(f"下载文件失败: {url}, 错误: {str(e)}")
            self.failed_downloads.append({'url': url, 'error': str(e)})
            return None
            
    def extract_resources_from_html(self, html_content, base_url):
        """从HTML中提取所有资源链接"""
        soup = BeautifulSoup(html_content, 'lxml')
        resources = set()
        
        # CSS文件
        for link in soup.find_all('link', rel='stylesheet'):
            href = link.get('href')
            if href:
                full_url = urljoin(base_url, href)
                resources.add(full_url)
                
        # JavaScript文件
        for script in soup.find_all('script', src=True):
            src = script.get('src')
            if src:
                full_url = urljoin(base_url, src)
                resources.add(full_url)
                
        # 图片
        for img in soup.find_all('img', src=True):
            src = img.get('src')
            if src:
                full_url = urljoin(base_url, src)
                resources.add(full_url)
                
        # 其他资源
        for elem in soup.find_all(['link', 'source'], href=True):
            href = elem.get('href')
            if href:
                full_url = urljoin(base_url, href)
                resources.add(full_url)
                
        return resources
        
    def extract_css_resources(self, css_content, base_url):
        """从CSS中提取资源链接"""
        resources = set()
        
        # 查找url()引用
        url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
        matches = re.findall(url_pattern, css_content, re.IGNORECASE)
        
        for match in matches:
            if not match.startswith('data:'):  # 跳过data URL
                full_url = urljoin(base_url, match)
                resources.add(full_url)
                
        return resources
        
    def download_page_and_resources(self, url):
        """下载页面及其所有资源"""
        logger.info(f"开始下载页面: {url}")
        
        # 下载主页面
        main_page_path = self.download_file(url)
        if not main_page_path:
            return False
            
        # 读取HTML内容
        with open(main_page_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
            
        # 提取资源链接
        resources = self.extract_resources_from_html(html_content, url)
        
        logger.info(f"发现 {len(resources)} 个资源文件")
        
        # 下载所有资源
        for resource_url in resources:
            # 只下载同域名的资源
            if self.base_url in resource_url or resource_url.startswith('/'):
                if not resource_url.startswith('http'):
                    resource_url = urljoin(self.base_url, resource_url)
                    
                self.download_file(resource_url)
                
        # 下载CSS中引用的资源
        css_files = [info['local_path'] for url, info in self.downloaded_files.items()
                    if info['content_type'].startswith('text/css')]

        for css_path in css_files:
            try:
                with open(css_path, 'r', encoding='utf-8') as f:
                    css_content = f.read()
                    
                css_resources = self.extract_css_resources(css_content, url)
                for css_resource in css_resources:
                    if self.base_url in css_resource or css_resource.startswith('/'):
                        if not css_resource.startswith('http'):
                            css_resource = urljoin(self.base_url, css_resource)
                        self.download_file(css_resource)
            except Exception as e:
                logger.warning(f"处理CSS文件失败: {css_path}, 错误: {e}")
                
        return True

    def download_multiple_pages(self, urls):
        """批量下载多个页面及其资源"""
        logger.info(f"开始批量下载 {len(urls)} 个页面")

        success_count = 0
        for i, url in enumerate(urls, 1):
            logger.info(f"进度: {i}/{len(urls)} - {url}")

            if self.download_page_and_resources(url):
                success_count += 1
            else:
                logger.error(f"页面下载失败: {url}")

        logger.info(f"批量下载完成: {success_count}/{len(urls)} 成功")
        return success_count

    def create_offline_index(self):
        """创建离线浏览的索引页面"""
        html_files = []

        # 收集所有HTML文件
        html_dir = os.path.join(self.output_dir, 'html')
        if os.path.exists(html_dir):
            for file in os.listdir(html_dir):
                if file.endswith('.html'):
                    html_files.append(file)

        # 生成索引页面
        index_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoreX MINER - 离线浏览</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }}
        .stats {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .file-list {{
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .file-item {{
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .file-item:last-child {{
            border-bottom: none;
        }}
        .file-item:hover {{
            background-color: #f8f9fa;
        }}
        .file-link {{
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }}
        .file-link:hover {{
            text-decoration: underline;
        }}
        .file-size {{
            color: #6c757d;
            font-size: 0.9em;
        }}
        .download-info {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🕷️ CoreX MINER 网站源码</h1>
        <p>离线浏览版本 - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="download-info">
        <h3>📊 下载统计</h3>
        <p><strong>总文件数:</strong> {len(self.downloaded_files)}</p>
        <p><strong>总大小:</strong> {self.format_size(self.total_size)}</p>
        <p><strong>成功下载:</strong> {len(self.downloaded_files)}</p>
        <p><strong>失败下载:</strong> {len(self.failed_downloads)}</p>
    </div>

    <div class="stats">
        <h3>📄 HTML页面 ({len(html_files)} 个)</h3>
    </div>

    <div class="file-list">
"""

        # 添加HTML文件链接
        for file in sorted(html_files):
            file_path = os.path.join(html_dir, file)
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0

            index_html += f"""
        <div class="file-item">
            <a href="html/{file}" class="file-link">{file}</a>
            <span class="file-size">{self.format_size(file_size)}</span>
        </div>
"""

        index_html += """
    </div>

    <div style="margin-top: 30px; text-align: center; color: #6c757d;">
        <p>🔧 由 CoreX MINER 源码下载器生成</p>
    </div>
</body>
</html>
"""

        # 保存索引文件
        index_path = os.path.join(self.output_dir, 'index.html')
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(index_html)

        logger.info(f"离线索引已创建: {index_path}")
        return index_path

    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def generate_download_report(self):
        """生成下载报告"""
        report = {
            'download_info': {
                'base_url': self.base_url,
                'output_dir': self.output_dir,
                'download_time': datetime.now().isoformat(),
                'total_files': len(self.downloaded_files),
                'total_size': self.total_size,
                'failed_downloads': len(self.failed_downloads)
            },
            'file_types': {},
            'downloaded_files': self.downloaded_files,
            'failed_downloads': self.failed_downloads
        }

        # 统计文件类型
        for url, info in self.downloaded_files.items():
            content_type = info['content_type']
            if content_type not in report['file_types']:
                report['file_types'][content_type] = {'count': 0, 'total_size': 0}

            report['file_types'][content_type]['count'] += 1
            report['file_types'][content_type]['total_size'] += info['size']

        # 保存报告
        report_path = os.path.join(self.output_dir, 'download_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"下载报告已保存: {report_path}")
        return report_path

    def download_site(self, start_urls=None):
        """下载整个网站"""
        if not start_urls:
            start_urls = [f"{self.base_url}/auth_m/1392053_ca4cea3e6d22bad"]

        logger.info(f"开始下载网站源码: {self.base_url}")
        logger.info(f"输出目录: {self.output_dir}")

        # 批量下载页面
        success_count = self.download_multiple_pages(start_urls)

        # 创建离线索引
        index_path = self.create_offline_index()

        # 生成下载报告
        report_path = self.generate_download_report()

        # 显示结果
        logger.info("网站源码下载完成！")
        logger.info(f"成功下载: {success_count}/{len(start_urls)} 个页面")
        logger.info(f"总文件数: {len(self.downloaded_files)}")
        logger.info(f"总大小: {self.format_size(self.total_size)}")
        logger.info(f"离线索引: {index_path}")
        logger.info(f"下载报告: {report_path}")

        return {
            'success_count': success_count,
            'total_files': len(self.downloaded_files),
            'total_size': self.total_size,
            'index_path': index_path,
            'report_path': report_path
        }
