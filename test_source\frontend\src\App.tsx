import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd-mobile';
import zhCN from 'antd-mobile/es/locales/zh-CN';
import { store } from './store';
import { useAuth } from './hooks/useAuth';

// 布局组件
import Layout from './components/layout/Layout';

// 页面组件
import MiningPage from './pages/MiningPage';
import PowerPage from './pages/PowerPage';
import EarnPage from './pages/EarnPage';
import WithdrawPage from './pages/WithdrawPage';
import TasksPage from './pages/TasksPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import CoreXMinerPage from './pages/CoreXMinerPage';

// 样式
import './styles/global.scss';

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <div className="loading-screen">加载中...</div>;
  }
  
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

// 公共路由组件
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  
  return isAuthenticated ? <Navigate to="/" replace /> : <>{children}</>;
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN}>
        <Router>
          <div className="app">
            <Routes>
              {/* 认证页面 */}
              <Route path="/login" element={
                <PublicRoute>
                  <LoginPage />
                </PublicRoute>
              } />
              <Route path="/register" element={
                <PublicRoute>
                  <RegisterPage />
                </PublicRoute>
              } />
              
              {/* 主应用页面 */}
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout>
                    <MiningPage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/power" element={
                <ProtectedRoute>
                  <Layout>
                    <PowerPage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/earn" element={
                <ProtectedRoute>
                  <Layout>
                    <EarnPage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/withdraw" element={
                <ProtectedRoute>
                  <Layout>
                    <WithdrawPage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/tasks" element={
                <ProtectedRoute>
                  <Layout>
                    <TasksPage />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/corex-miner" element={
                <ProtectedRoute>
                  <CoreXMinerPage />
                </ProtectedRoute>
              } />

              {/* 404页面 */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </Router>
      </ConfigProvider>
    </Provider>
  );
};

export default App; 