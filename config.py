#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫配置文件
"""

# 目标网站配置
TARGET_URLS = {
    'main': 'https://app-core-x.online//auth_m/1392053_ca4cea3e6d22bad',
    'base': 'https://app-core-x.online'
}

# 请求配置
REQUEST_CONFIG = {
    'timeout': 10,
    'max_retries': 3,
    'retry_delay': 2,  # 秒
    'random_delay_range': (1, 3),  # 随机延时范围
}

# 用户代理配置
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
]

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'rotation': '1 day',
    'retention': '7 days',
    'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}'
}

# 数据保存配置
SAVE_CONFIG = {
    'data_dir': 'data',
    'logs_dir': 'logs',
    'save_json': True,
    'save_excel': True,
    'save_csv': False
}

# 爬取目标配置
CRAWL_TARGETS = {
    'extract_links': True,
    'extract_images': True,
    'extract_text': True,
    'extract_telegram_links': True,
    'extract_mining_info': True,
    'extract_metadata': True
}
